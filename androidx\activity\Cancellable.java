package androidx.activity;

import kotlin.Metadata;

/* compiled from: Cancellable.kt */
@Metadata(m162d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\b`\u0018\u00002\u00020\u0001J\b\u0010\u0002\u001a\u00020\u0003H&ø\u0001\u0000\u0082\u0002\u0006\n\u0004\b!0\u0001¨\u0006\u0004À\u0006\u0001"}, m163d2 = {"Landroidx/activity/Cancellable;", "", "cancel", "", "activity_release"}, m164k = 1, m165mv = {1, 8, 0}, m167xi = 48)
/* loaded from: classes.dex */
public interface Cancellable {
    void cancel();
}
