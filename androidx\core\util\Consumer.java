package androidx.core.util;

import androidx.exifinterface.media.ExifInterface;
import kotlin.Metadata;

/* compiled from: Consumer.kt */
@Metadata(m162d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\bæ\u0080\u0001\u0018\u0000*\u0004\b\u0000\u0010\u00012\u00020\u0002J\u0015\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00028\u0000H&¢\u0006\u0002\u0010\u0006ø\u0001\u0000\u0082\u0002\u0006\n\u0004\b!0\u0001¨\u0006\u0007À\u0006\u0001"}, m163d2 = {"Landroidx/core/util/Consumer;", ExifInterface.GPS_DIRECTION_TRUE, "", "accept", "", "value", "(Ljava/lang/Object;)V", "core_release"}, m164k = 1, m165mv = {1, 8, 0}, m167xi = 48)
/* loaded from: classes.dex */
public interface Consumer<T> {
    void accept(T value);
}
