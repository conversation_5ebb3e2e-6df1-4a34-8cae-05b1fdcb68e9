package androidx.core;

import com.nsdisplay.a01.C1201R;

/* renamed from: androidx.core.R */
/* loaded from: classes3.dex */
public final class C0189R {

    /* renamed from: androidx.core.R$attr */
    public static final class attr {
        public static int alpha = **********;
        public static int font = **********;
        public static int fontProviderAuthority = **********;
        public static int fontProviderCerts = **********;
        public static int fontProviderFetchStrategy = **********;
        public static int fontProviderFetchTimeout = **********;
        public static int fontProviderPackage = **********;
        public static int fontProviderQuery = **********;
        public static int fontProviderSystemFontFamily = **********;
        public static int fontStyle = **********;
        public static int fontVariationSettings = **********;
        public static int fontWeight = **********;
        public static int lStar = **********;
        public static int nestedScrollViewStyle = **********;
        public static int queryPatterns = **********;
        public static int shortcutMatchRequired = **********;
        public static int ttcIndex = **********;

        private attr() {
        }
    }

    /* renamed from: androidx.core.R$color */
    public static final class color {
        public static int androidx_core_ripple_material_light = **********;
        public static int androidx_core_secondary_text_default_material_light = **********;
        public static int call_notification_answer_color = **********;
        public static int call_notification_decline_color = **********;
        public static int notification_action_color_filter = **********;
        public static int notification_icon_bg_color = 2131034870;

        private color() {
        }
    }

    /* renamed from: androidx.core.R$dimen */
    public static final class dimen {
        public static int compat_button_inset_horizontal_material = 2131099736;
        public static int compat_button_inset_vertical_material = 2131099737;
        public static int compat_button_padding_horizontal_material = 2131099738;
        public static int compat_button_padding_vertical_material = 2131099739;
        public static int compat_control_corner_material = 2131099740;
        public static int compat_notification_large_icon_max_height = 2131099741;
        public static int compat_notification_large_icon_max_width = 2131099742;
        public static int notification_action_icon_size = 2131100437;
        public static int notification_action_text_size = 2131100438;
        public static int notification_big_circle_margin = 2131100439;
        public static int notification_content_margin_start = 2131100440;
        public static int notification_large_icon_height = 2131100441;
        public static int notification_large_icon_width = 2131100442;
        public static int notification_main_column_padding_top = 2131100443;
        public static int notification_media_narrow_margin = 2131100444;
        public static int notification_right_icon_size = 2131100445;
        public static int notification_right_side_padding_top = 2131100446;
        public static int notification_small_icon_background_padding = 2131100447;
        public static int notification_small_icon_size_as_large = 2131100448;
        public static int notification_subtext_size = 2131100449;
        public static int notification_top_pad = 2131100450;
        public static int notification_top_pad_large_text = 2131100451;

        private dimen() {
        }
    }

    /* renamed from: androidx.core.R$drawable */
    public static final class drawable {
        public static int ic_call_answer = 2131165326;
        public static int ic_call_answer_low = 2131165327;
        public static int ic_call_answer_video = 2131165328;
        public static int ic_call_answer_video_low = 2131165329;
        public static int ic_call_decline = 2131165330;
        public static int ic_call_decline_low = 2131165331;
        public static int notification_action_background = 2131165418;
        public static int notification_bg = 2131165419;
        public static int notification_bg_low = 2131165420;
        public static int notification_bg_low_normal = 2131165421;
        public static int notification_bg_low_pressed = 2131165422;
        public static int notification_bg_normal = 2131165423;
        public static int notification_bg_normal_pressed = 2131165424;
        public static int notification_icon_background = 2131165425;
        public static int notification_oversize_large_icon_bg = 2131165426;
        public static int notification_template_icon_bg = 2131165427;
        public static int notification_template_icon_low_bg = 2131165428;
        public static int notification_tile_bg = 2131165429;
        public static int notify_panel_notification_icon_bg = 2131165430;

        private drawable() {
        }
    }

    /* renamed from: androidx.core.R$id */
    public static final class id {
        public static int accessibility_action_clickable_span = 2131230735;
        public static int accessibility_custom_action_0 = 2131230736;
        public static int accessibility_custom_action_1 = 2131230737;
        public static int accessibility_custom_action_10 = 2131230738;
        public static int accessibility_custom_action_11 = 2131230739;
        public static int accessibility_custom_action_12 = 2131230740;
        public static int accessibility_custom_action_13 = 2131230741;
        public static int accessibility_custom_action_14 = 2131230742;
        public static int accessibility_custom_action_15 = 2131230743;
        public static int accessibility_custom_action_16 = 2131230744;
        public static int accessibility_custom_action_17 = 2131230745;
        public static int accessibility_custom_action_18 = 2131230746;
        public static int accessibility_custom_action_19 = 2131230747;
        public static int accessibility_custom_action_2 = 2131230748;
        public static int accessibility_custom_action_20 = 2131230749;
        public static int accessibility_custom_action_21 = 2131230750;
        public static int accessibility_custom_action_22 = 2131230751;
        public static int accessibility_custom_action_23 = 2131230752;
        public static int accessibility_custom_action_24 = 2131230753;
        public static int accessibility_custom_action_25 = 2131230754;
        public static int accessibility_custom_action_26 = 2131230755;
        public static int accessibility_custom_action_27 = 2131230756;
        public static int accessibility_custom_action_28 = 2131230757;
        public static int accessibility_custom_action_29 = 2131230758;
        public static int accessibility_custom_action_3 = 2131230759;
        public static int accessibility_custom_action_30 = 2131230760;
        public static int accessibility_custom_action_31 = 2131230761;
        public static int accessibility_custom_action_4 = 2131230762;
        public static int accessibility_custom_action_5 = 2131230763;
        public static int accessibility_custom_action_6 = 2131230764;
        public static int accessibility_custom_action_7 = 2131230765;
        public static int accessibility_custom_action_8 = 2131230766;
        public static int accessibility_custom_action_9 = 2131230767;
        public static int action_container = 2131230778;
        public static int action_divider = 2131230780;
        public static int action_image = 2131230781;
        public static int action_text = 2131230787;
        public static int actions = 2131230788;
        public static int async = 2131230803;
        public static int blocking = 2131230813;
        public static int chronometer = 2131230838;
        public static int dialog_button = 2131230877;
        public static int edit_text_id = 2131230904;
        public static int forever = 2131230928;
        public static int hide_ime_id = 2131230941;
        public static int icon = 2131230947;
        public static int icon_group = 2131230948;
        public static int info = 2131230965;
        public static int italic = 2131230968;
        public static int line1 = 2131230978;
        public static int line3 = 2131230979;
        public static int normal = 2131231069;
        public static int notification_background = 2131231071;
        public static int notification_main_column = 2131231072;
        public static int notification_main_column_container = 2131231073;
        public static int right_icon = 2131231120;
        public static int right_side = 2131231121;
        public static int tag_accessibility_actions = 2131231193;
        public static int tag_accessibility_clickable_spans = 2131231194;
        public static int tag_accessibility_heading = 2131231195;
        public static int tag_accessibility_pane_title = 2131231196;
        public static int tag_on_apply_window_listener = 2131231197;
        public static int tag_on_receive_content_listener = 2131231198;
        public static int tag_on_receive_content_mime_types = 2131231199;
        public static int tag_screen_reader_focusable = 2131231200;
        public static int tag_state_description = 2131231201;
        public static int tag_transition_group = 2131231202;
        public static int tag_unhandled_key_event_manager = 2131231203;
        public static int tag_unhandled_key_listeners = 2131231204;
        public static int tag_window_insets_animation_callback = 2131231205;
        public static int text = 2131231206;
        public static int text2 = 2131231207;
        public static int time = 2131231243;
        public static int title = 2131231244;

        private id() {
        }
    }

    /* renamed from: androidx.core.R$integer */
    public static final class integer {
        public static int status_bar_notification_info_maxnum = 2131296325;

        private integer() {
        }
    }

    /* renamed from: androidx.core.R$layout */
    public static final class layout {
        public static int custom_dialog = 2131427358;
        public static int ime_base_split_test_activity = 2131427386;
        public static int ime_secondary_split_test_activity = 2131427387;
        public static int notification_action = 2131427439;
        public static int notification_action_tombstone = 2131427440;
        public static int notification_template_custom_big = 2131427441;
        public static int notification_template_icon_group = 2131427442;
        public static int notification_template_part_chronometer = 2131427443;
        public static int notification_template_part_time = 2131427444;

        private layout() {
        }
    }

    /* renamed from: androidx.core.R$string */
    public static final class string {
        public static int call_notification_answer_action = 2131820580;
        public static int call_notification_answer_video_action = 2131820581;
        public static int call_notification_decline_action = 2131820582;
        public static int call_notification_hang_up_action = 2131820583;
        public static int call_notification_incoming_text = 2131820584;
        public static int call_notification_ongoing_text = 2131820585;
        public static int call_notification_screening_text = 2131820586;
        public static int status_bar_notification_info_overflow = 2131820714;

        private string() {
        }
    }

    /* renamed from: androidx.core.R$style */
    public static final class style {
        public static int TextAppearance_Compat_Notification = 2131886539;
        public static int TextAppearance_Compat_Notification_Info = 2131886540;
        public static int TextAppearance_Compat_Notification_Line2 = 2131886541;
        public static int TextAppearance_Compat_Notification_Time = 2131886542;
        public static int TextAppearance_Compat_Notification_Title = 2131886543;
        public static int Widget_Compat_NotificationActionContainer = 2131886908;
        public static int Widget_Compat_NotificationActionText = 2131886909;

        private style() {
        }
    }

    /* renamed from: androidx.core.R$styleable */
    public static final class styleable {
        public static int Capability_queryPatterns = 0;
        public static int Capability_shortcutMatchRequired = 1;
        public static int ColorStateListItem_alpha = 3;
        public static int ColorStateListItem_android_alpha = 1;
        public static int ColorStateListItem_android_color = 0;
        public static int ColorStateListItem_android_lStar = 2;
        public static int ColorStateListItem_lStar = 4;
        public static int FontFamilyFont_android_font = 0;
        public static int FontFamilyFont_android_fontStyle = 2;
        public static int FontFamilyFont_android_fontVariationSettings = 4;
        public static int FontFamilyFont_android_fontWeight = 1;
        public static int FontFamilyFont_android_ttcIndex = 3;
        public static int FontFamilyFont_font = 5;
        public static int FontFamilyFont_fontStyle = 6;
        public static int FontFamilyFont_fontVariationSettings = 7;
        public static int FontFamilyFont_fontWeight = 8;
        public static int FontFamilyFont_ttcIndex = 9;
        public static int FontFamily_fontProviderAuthority = 0;
        public static int FontFamily_fontProviderCerts = 1;
        public static int FontFamily_fontProviderFetchStrategy = 2;
        public static int FontFamily_fontProviderFetchTimeout = 3;
        public static int FontFamily_fontProviderPackage = 4;
        public static int FontFamily_fontProviderQuery = 5;
        public static int FontFamily_fontProviderSystemFontFamily = 6;
        public static int GradientColorItem_android_color = 0;
        public static int GradientColorItem_android_offset = 1;
        public static int GradientColor_android_centerColor = 7;
        public static int GradientColor_android_centerX = 3;
        public static int GradientColor_android_centerY = 4;
        public static int GradientColor_android_endColor = 1;
        public static int GradientColor_android_endX = 10;
        public static int GradientColor_android_endY = 11;
        public static int GradientColor_android_gradientRadius = 5;
        public static int GradientColor_android_startColor = 0;
        public static int GradientColor_android_startX = 8;
        public static int GradientColor_android_startY = 9;
        public static int GradientColor_android_tileMode = 6;
        public static int GradientColor_android_type = 2;
        public static int[] Capability = {C1201R.attr.queryPatterns, C1201R.attr.shortcutMatchRequired};
        public static int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, android.R.attr.lStar, C1201R.attr.alpha, C1201R.attr.lStar};
        public static int[] FontFamily = {C1201R.attr.fontProviderAuthority, C1201R.attr.fontProviderCerts, C1201R.attr.fontProviderFetchStrategy, C1201R.attr.fontProviderFetchTimeout, C1201R.attr.fontProviderPackage, C1201R.attr.fontProviderQuery, C1201R.attr.fontProviderSystemFontFamily};
        public static int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, C1201R.attr.font, C1201R.attr.fontStyle, C1201R.attr.fontVariationSettings, C1201R.attr.fontWeight, C1201R.attr.ttcIndex};
        public static int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};

        private styleable() {
        }
    }

    private C0189R() {
    }
}
