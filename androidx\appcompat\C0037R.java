package androidx.appcompat;

import com.nsdisplay.a01.C1201R;

/* renamed from: androidx.appcompat.R */
/* loaded from: classes3.dex */
public final class C0037R {

    /* renamed from: androidx.appcompat.R$anim */
    public static final class anim {
        public static int abc_fade_in = 2130771968;
        public static int abc_fade_out = 2130771969;
        public static int abc_grow_fade_in_from_bottom = 2130771970;
        public static int abc_popup_enter = 2130771971;
        public static int abc_popup_exit = 2130771972;
        public static int abc_shrink_fade_out_from_bottom = 2130771973;
        public static int abc_slide_in_bottom = 2130771974;
        public static int abc_slide_in_top = 2130771975;
        public static int abc_slide_out_bottom = 2130771976;
        public static int abc_slide_out_top = 2130771977;
        public static int abc_tooltip_enter = 2130771978;
        public static int abc_tooltip_exit = 2130771979;
        public static int btn_checkbox_to_checked_box_inner_merged_animation = 2130771980;
        public static int btn_checkbox_to_checked_box_outer_merged_animation = 2130771981;
        public static int btn_checkbox_to_checked_icon_null_animation = 2130771982;
        public static int btn_checkbox_to_unchecked_box_inner_merged_animation = 2130771983;
        public static int btn_checkbox_to_unchecked_check_path_merged_animation = 2130771984;
        public static int btn_checkbox_to_unchecked_icon_null_animation = 2130771985;
        public static int btn_radio_to_off_mtrl_dot_group_animation = 2130771986;
        public static int btn_radio_to_off_mtrl_ring_outer_animation = 2130771987;
        public static int btn_radio_to_off_mtrl_ring_outer_path_animation = 2130771988;
        public static int btn_radio_to_on_mtrl_dot_group_animation = 2130771989;
        public static int btn_radio_to_on_mtrl_ring_outer_animation = 2130771990;
        public static int btn_radio_to_on_mtrl_ring_outer_path_animation = 2130771991;

        private anim() {
        }
    }

    /* renamed from: androidx.appcompat.R$attr */
    public static final class attr {
        public static int actionBarDivider = 2130903043;
        public static int actionBarItemBackground = 2130903044;
        public static int actionBarPopupTheme = 2130903045;
        public static int actionBarSize = 2130903046;
        public static int actionBarSplitStyle = 2130903047;
        public static int actionBarStyle = 2130903048;
        public static int actionBarTabBarStyle = 2130903049;
        public static int actionBarTabStyle = 2130903050;
        public static int actionBarTabTextStyle = 2130903051;
        public static int actionBarTheme = 2130903052;
        public static int actionBarWidgetTheme = 2130903053;
        public static int actionButtonStyle = 2130903054;
        public static int actionDropDownStyle = 2130903055;
        public static int actionLayout = 2130903056;
        public static int actionMenuTextAppearance = 2130903057;
        public static int actionMenuTextColor = 2130903058;
        public static int actionModeBackground = 2130903059;
        public static int actionModeCloseButtonStyle = 2130903060;
        public static int actionModeCloseContentDescription = 2130903061;
        public static int actionModeCloseDrawable = 2130903062;
        public static int actionModeCopyDrawable = 2130903063;
        public static int actionModeCutDrawable = 2130903064;
        public static int actionModeFindDrawable = 2130903065;
        public static int actionModePasteDrawable = 2130903066;
        public static int actionModePopupWindowStyle = 2130903067;
        public static int actionModeSelectAllDrawable = **********;
        public static int actionModeShareDrawable = **********;
        public static int actionModeSplitBackground = **********;
        public static int actionModeStyle = **********;
        public static int actionModeTheme = **********;
        public static int actionModeWebSearchDrawable = **********;
        public static int actionOverflowButtonStyle = **********;
        public static int actionOverflowMenuStyle = **********;
        public static int actionProviderClass = **********;
        public static int actionViewClass = **********;
        public static int activityChooserViewStyle = **********;
        public static int alertDialogButtonGroupStyle = **********;
        public static int alertDialogCenterButtons = **********;
        public static int alertDialogStyle = **********;
        public static int alertDialogTheme = **********;
        public static int allowStacking = **********;
        public static int alphabeticModifiers = **********;
        public static int arrowHeadLength = **********;
        public static int arrowShaftLength = **********;
        public static int autoCompleteTextViewStyle = **********;
        public static int autoSizeMaxTextSize = **********;
        public static int autoSizeMinTextSize = **********;
        public static int autoSizePresetSizes = **********;
        public static int autoSizeStepGranularity = **********;
        public static int autoSizeTextType = **********;
        public static int background = 2130903116;
        public static int backgroundSplit = 2130903123;
        public static int backgroundStacked = 2130903124;
        public static int backgroundTint = 2130903125;
        public static int backgroundTintMode = 2130903126;
        public static int barLength = 2130903144;
        public static int borderlessButtonStyle = 2130903164;
        public static int buttonBarButtonStyle = 2130903183;
        public static int buttonBarNegativeButtonStyle = 2130903184;
        public static int buttonBarNeutralButtonStyle = 2130903185;
        public static int buttonBarPositiveButtonStyle = 2130903186;
        public static int buttonBarStyle = 2130903187;
        public static int buttonCompat = 2130903188;
        public static int buttonGravity = 2130903189;
        public static int buttonIconDimen = 2130903191;
        public static int buttonPanelSideLayout = 2130903194;
        public static int buttonStyle = 2130903195;
        public static int buttonStyleSmall = 2130903196;
        public static int buttonTint = 2130903197;
        public static int buttonTintMode = 2130903198;
        public static int checkMarkCompat = 2130903220;
        public static int checkMarkTint = 2130903221;
        public static int checkMarkTintMode = 2130903222;
        public static int checkboxStyle = 2130903223;
        public static int checkedTextViewStyle = 2130903234;
        public static int closeIcon = 2130903269;
        public static int closeItemLayout = 2130903276;
        public static int collapseContentDescription = 2130903277;
        public static int collapseIcon = 2130903278;
        public static int color = 2130903288;
        public static int colorAccent = 2130903289;
        public static int colorBackgroundFloating = 2130903290;
        public static int colorButtonNormal = 2130903291;
        public static int colorControlActivated = 2130903293;
        public static int colorControlHighlight = 2130903294;
        public static int colorControlNormal = 2130903295;
        public static int colorError = 2130903296;
        public static int colorPrimary = 2130903321;
        public static int colorPrimaryDark = 2130903323;
        public static int colorSwitchThumbNormal = 2130903344;
        public static int commitIcon = 2130903349;
        public static int contentDescription = 2130903359;
        public static int contentInsetEnd = 2130903360;
        public static int contentInsetEndWithActions = 2130903361;
        public static int contentInsetLeft = 2130903362;
        public static int contentInsetRight = 2130903363;
        public static int contentInsetStart = 2130903364;
        public static int contentInsetStartWithNavigation = 2130903365;
        public static int controlBackground = 2130903375;
        public static int customNavigationLayout = 2130903406;
        public static int defaultQueryHint = 2130903419;
        public static int dialogCornerRadius = 2130903426;
        public static int dialogPreferredPadding = 2130903427;
        public static int dialogTheme = 2130903428;
        public static int displayOptions = 2130903429;
        public static int divider = 2130903430;
        public static int dividerHorizontal = 2130903432;
        public static int dividerPadding = 2130903435;
        public static int dividerVertical = 2130903437;
        public static int drawableBottomCompat = 2130903442;
        public static int drawableEndCompat = 2130903443;
        public static int drawableLeftCompat = 2130903444;
        public static int drawableRightCompat = 2130903445;
        public static int drawableSize = 2130903446;
        public static int drawableStartCompat = 2130903447;
        public static int drawableTint = 2130903448;
        public static int drawableTintMode = 2130903449;
        public static int drawableTopCompat = 2130903450;
        public static int drawerArrowStyle = 2130903451;
        public static int dropDownListViewStyle = 2130903455;
        public static int dropdownListPreferredItemHeight = 2130903456;
        public static int editTextBackground = 2130903459;
        public static int editTextColor = 2130903460;
        public static int editTextStyle = 2130903461;
        public static int elevation = 2130903462;
        public static int emojiCompatEnabled = 2130903466;
        public static int expandActivityOverflowButtonDrawable = 2130903491;
        public static int firstBaselineToTopHeight = 2130903526;
        public static int fontFamily = 2130903562;
        public static int fontVariationSettings = 2130903571;
        public static int gapBetweenBars = 2130903577;
        public static int goIcon = 2130903579;
        public static int height = 2130903585;
        public static int hideOnContentScroll = 2130903593;
        public static int homeAsUpIndicator = 2130903599;
        public static int homeLayout = 2130903600;
        public static int icon = 2130903604;
        public static int iconTint = 2130903610;
        public static int iconTintMode = 2130903611;
        public static int iconifiedByDefault = 2130903612;
        public static int imageButtonStyle = 2130903615;
        public static int indeterminateProgressStyle = 2130903621;
        public static int initialActivityCount = 2130903628;
        public static int isLightTheme = 2130903630;
        public static int itemPadding = 2130903644;
        public static int lastBaselineToBottomHeight = 2130903672;
        public static int layout = 2130903675;
        public static int lineHeight = 2130903752;
        public static int listChoiceBackgroundIndicator = 2130903755;
        public static int listChoiceIndicatorMultipleAnimated = 2130903756;
        public static int listChoiceIndicatorSingleAnimated = 2130903757;
        public static int listDividerAlertDialog = 2130903758;
        public static int listItemLayout = 2130903759;
        public static int listLayout = 2130903760;
        public static int listMenuViewStyle = 2130903761;
        public static int listPopupWindowStyle = 2130903762;
        public static int listPreferredItemHeight = 2130903763;
        public static int listPreferredItemHeightLarge = 2130903764;
        public static int listPreferredItemHeightSmall = 2130903765;
        public static int listPreferredItemPaddingEnd = 2130903766;
        public static int listPreferredItemPaddingLeft = 2130903767;
        public static int listPreferredItemPaddingRight = 2130903768;
        public static int listPreferredItemPaddingStart = 2130903769;
        public static int logo = 2130903770;
        public static int logoDescription = 2130903772;
        public static int maxButtonHeight = 2130903827;
        public static int measureWithLargestChild = 2130903835;
        public static int menu = 2130903836;
        public static int multiChoiceItemLayout = 2130903898;
        public static int navigationContentDescription = 2130903900;
        public static int navigationIcon = 2130903901;
        public static int navigationMode = 2130903903;
        public static int numericModifiers = 2130903911;
        public static int overlapAnchor = 2130903920;
        public static int paddingBottomNoButtons = 2130903922;
        public static int paddingEnd = 2130903924;
        public static int paddingStart = 2130903927;
        public static int paddingTopNoTitle = 2130903929;
        public static int panelBackground = 2130903931;
        public static int panelMenuListTheme = 2130903932;
        public static int panelMenuListWidth = 2130903933;
        public static int popupMenuStyle = 2130903959;
        public static int popupTheme = 2130903960;
        public static int popupWindowStyle = 2130903961;
        public static int preserveIconSpacing = 2130903966;
        public static int progressBarPadding = 2130903969;
        public static int progressBarStyle = 2130903970;
        public static int queryBackground = 2130903974;
        public static int queryHint = 2130903975;
        public static int radioButtonStyle = 2130903977;
        public static int ratingBarStyle = 2130903979;
        public static int ratingBarStyleIndicator = 2130903980;
        public static int ratingBarStyleSmall = 2130903981;
        public static int searchHintIcon = 2130904004;
        public static int searchIcon = 2130904005;
        public static int searchViewStyle = 2130904007;
        public static int seekBarStyle = 2130904010;
        public static int selectableItemBackground = 2130904011;
        public static int selectableItemBackgroundBorderless = 2130904012;
        public static int showAsAction = 2130904030;
        public static int showDividers = 2130904032;
        public static int showText = 2130904036;
        public static int showTitle = 2130904037;
        public static int singleChoiceItemLayout = 2130904045;
        public static int spinBars = 2130904054;
        public static int spinnerDropDownItemStyle = 2130904055;
        public static int spinnerStyle = 2130904056;
        public static int splitTrack = 2130904062;
        public static int srcCompat = 2130904068;
        public static int state_above_anchor = 2130904079;
        public static int subMenuArrow = 2130904093;
        public static int submitBackground = 2130904098;
        public static int subtitle = 2130904099;
        public static int subtitleTextAppearance = 2130904101;
        public static int subtitleTextColor = 2130904102;
        public static int subtitleTextStyle = 2130904103;
        public static int suggestionRowLayout = 2130904107;
        public static int switchMinWidth = 2130904109;
        public static int switchPadding = 2130904110;
        public static int switchStyle = 2130904111;
        public static int switchTextAppearance = 2130904112;
        public static int textAllCaps = 2130904147;
        public static int textAppearanceLargePopupMenu = 2130904170;
        public static int textAppearanceListItem = 2130904172;
        public static int textAppearanceListItemSecondary = 2130904173;
        public static int textAppearanceListItemSmall = 2130904174;
        public static int textAppearancePopupMenuHeader = 2130904176;
        public static int textAppearanceSearchResultSubtitle = 2130904177;
        public static int textAppearanceSearchResultTitle = 2130904178;
        public static int textAppearanceSmallPopupMenu = 2130904179;
        public static int textColorAlertDialogListItem = 2130904190;
        public static int textColorSearchUrl = 2130904191;
        public static int textLocale = 2130904202;
        public static int theme = 2130904212;
        public static int thickness = 2130904213;
        public static int thumbTextPadding = 2130904224;
        public static int thumbTint = 2130904225;
        public static int thumbTintMode = 2130904226;
        public static int tickMark = 2130904232;
        public static int tickMarkTint = 2130904233;
        public static int tickMarkTintMode = 2130904234;
        public static int tint = 2130904238;
        public static int tintMode = 2130904239;
        public static int title = 2130904241;
        public static int titleMargin = 2130904245;
        public static int titleMarginBottom = 2130904246;
        public static int titleMarginEnd = 2130904247;
        public static int titleMarginStart = 2130904248;
        public static int titleMarginTop = 2130904249;
        public static int titleMargins = 2130904250;
        public static int titleTextAppearance = 2130904252;
        public static int titleTextColor = 2130904253;
        public static int titleTextStyle = 2130904255;
        public static int toolbarNavigationButtonStyle = 2130904258;
        public static int toolbarStyle = 2130904259;
        public static int tooltipForegroundColor = 2130904261;
        public static int tooltipFrameBackground = 2130904262;
        public static int tooltipText = 2130904264;
        public static int track = 2130904269;
        public static int trackTint = 2130904281;
        public static int trackTintMode = 2130904282;
        public static int viewInflaterClass = 2130904301;
        public static int voiceIcon = 2130904307;
        public static int windowActionBar = 2130904315;
        public static int windowActionBarOverlay = 2130904316;
        public static int windowActionModeOverlay = 2130904317;
        public static int windowFixedHeightMajor = 2130904318;
        public static int windowFixedHeightMinor = 2130904319;
        public static int windowFixedWidthMajor = 2130904320;
        public static int windowFixedWidthMinor = 2130904321;
        public static int windowMinWidthMajor = 2130904322;
        public static int windowMinWidthMinor = 2130904323;
        public static int windowNoTitle = 2130904324;

        private attr() {
        }
    }

    /* renamed from: androidx.appcompat.R$bool */
    public static final class bool {
        public static int abc_action_bar_embed_tabs = 2130968576;
        public static int abc_config_actionMenuItemAllCaps = 2130968577;

        private bool() {
        }
    }

    /* renamed from: androidx.appcompat.R$color */
    public static final class color {
        public static int abc_background_cache_hint_selector_material_dark = 2131034112;
        public static int abc_background_cache_hint_selector_material_light = 2131034113;
        public static int abc_btn_colored_borderless_text_material = 2131034114;
        public static int abc_btn_colored_text_material = 2131034115;
        public static int abc_color_highlight_material = 2131034116;
        public static int abc_decor_view_status_guard = 2131034117;
        public static int abc_decor_view_status_guard_light = 2131034118;
        public static int abc_hint_foreground_material_dark = 2131034119;
        public static int abc_hint_foreground_material_light = 2131034120;
        public static int abc_primary_text_disable_only_material_dark = 2131034121;
        public static int abc_primary_text_disable_only_material_light = 2131034122;
        public static int abc_primary_text_material_dark = 2131034123;
        public static int abc_primary_text_material_light = 2131034124;
        public static int abc_search_url_text = 2131034125;
        public static int abc_search_url_text_normal = 2131034126;
        public static int abc_search_url_text_pressed = 2131034127;
        public static int abc_search_url_text_selected = 2131034128;
        public static int abc_secondary_text_material_dark = 2131034129;
        public static int abc_secondary_text_material_light = 2131034130;
        public static int abc_tint_btn_checkable = 2131034131;
        public static int abc_tint_default = 2131034132;
        public static int abc_tint_edittext = 2131034133;
        public static int abc_tint_seek_thumb = 2131034134;
        public static int abc_tint_spinner = 2131034135;
        public static int abc_tint_switch_track = 2131034136;
        public static int accent_material_dark = 2131034137;
        public static int accent_material_light = 2131034138;
        public static int background_floating_material_dark = 2131034141;
        public static int background_floating_material_light = 2131034142;
        public static int background_material_dark = 2131034143;
        public static int background_material_light = 2131034144;
        public static int bright_foreground_disabled_material_dark = 2131034150;
        public static int bright_foreground_disabled_material_light = 2131034151;
        public static int bright_foreground_inverse_material_dark = 2131034152;
        public static int bright_foreground_inverse_material_light = 2131034153;
        public static int bright_foreground_material_dark = 2131034154;
        public static int bright_foreground_material_light = 2131034155;
        public static int button_material_dark = 2131034156;
        public static int button_material_light = 2131034157;
        public static int dim_foreground_disabled_material_dark = 2131034202;
        public static int dim_foreground_disabled_material_light = 2131034203;
        public static int dim_foreground_material_dark = 2131034204;
        public static int dim_foreground_material_light = 2131034205;
        public static int error_color_material_dark = 2131034206;
        public static int error_color_material_light = 2131034207;
        public static int foreground_material_dark = 2131034208;
        public static int foreground_material_light = 2131034209;
        public static int highlighted_text_material_dark = 2131034210;
        public static int highlighted_text_material_light = 2131034211;
        public static int material_blue_grey_800 = 2131034648;
        public static int material_blue_grey_900 = 2131034649;
        public static int material_blue_grey_950 = 2131034650;
        public static int material_deep_teal_200 = 2131034652;
        public static int material_deep_teal_500 = 2131034653;
        public static int material_grey_100 = 2131034728;
        public static int material_grey_300 = 2131034729;
        public static int material_grey_50 = 2131034730;
        public static int material_grey_600 = 2131034731;
        public static int material_grey_800 = 2131034732;
        public static int material_grey_850 = 2131034733;
        public static int material_grey_900 = 2131034734;
        public static int primary_dark_material_dark = 2131034871;
        public static int primary_dark_material_light = 2131034872;
        public static int primary_material_dark = 2131034873;
        public static int primary_material_light = 2131034874;
        public static int primary_text_default_material_dark = 2131034875;
        public static int primary_text_default_material_light = 2131034876;
        public static int primary_text_disabled_material_dark = 2131034877;
        public static int primary_text_disabled_material_light = 2131034878;
        public static int ripple_material_dark = 2131034882;
        public static int ripple_material_light = 2131034883;
        public static int secondary_text_default_material_dark = 2131034885;
        public static int secondary_text_default_material_light = 2131034886;
        public static int secondary_text_disabled_material_dark = 2131034887;
        public static int secondary_text_disabled_material_light = 2131034888;
        public static int switch_thumb_disabled_material_dark = 2131034889;
        public static int switch_thumb_disabled_material_light = 2131034890;
        public static int switch_thumb_material_dark = 2131034891;
        public static int switch_thumb_material_light = 2131034892;
        public static int switch_thumb_normal_material_dark = 2131034893;
        public static int switch_thumb_normal_material_light = 2131034894;
        public static int tooltip_background_dark = 2131034897;
        public static int tooltip_background_light = 2131034898;

        private color() {
        }
    }

    /* renamed from: androidx.appcompat.R$dimen */
    public static final class dimen {
        public static int abc_action_bar_content_inset_material = 2131099648;
        public static int abc_action_bar_content_inset_with_nav = 2131099649;
        public static int abc_action_bar_default_height_material = 2131099650;
        public static int abc_action_bar_default_padding_end_material = 2131099651;
        public static int abc_action_bar_default_padding_start_material = 2131099652;
        public static int abc_action_bar_elevation_material = 2131099653;
        public static int abc_action_bar_icon_vertical_padding_material = 2131099654;
        public static int abc_action_bar_overflow_padding_end_material = 2131099655;
        public static int abc_action_bar_overflow_padding_start_material = 2131099656;
        public static int abc_action_bar_stacked_max_height = 2131099657;
        public static int abc_action_bar_stacked_tab_max_width = 2131099658;
        public static int abc_action_bar_subtitle_bottom_margin_material = 2131099659;
        public static int abc_action_bar_subtitle_top_margin_material = 2131099660;
        public static int abc_action_button_min_height_material = 2131099661;
        public static int abc_action_button_min_width_material = 2131099662;
        public static int abc_action_button_min_width_overflow_material = 2131099663;
        public static int abc_alert_dialog_button_bar_height = 2131099664;
        public static int abc_alert_dialog_button_dimen = 2131099665;
        public static int abc_button_inset_horizontal_material = 2131099666;
        public static int abc_button_inset_vertical_material = 2131099667;
        public static int abc_button_padding_horizontal_material = 2131099668;
        public static int abc_button_padding_vertical_material = 2131099669;
        public static int abc_cascading_menus_min_smallest_width = 2131099670;
        public static int abc_config_prefDialogWidth = 2131099671;
        public static int abc_control_corner_material = 2131099672;
        public static int abc_control_inset_material = 2131099673;
        public static int abc_control_padding_material = 2131099674;
        public static int abc_dialog_corner_radius_material = 2131099675;
        public static int abc_dialog_fixed_height_major = 2131099676;
        public static int abc_dialog_fixed_height_minor = 2131099677;
        public static int abc_dialog_fixed_width_major = 2131099678;
        public static int abc_dialog_fixed_width_minor = 2131099679;
        public static int abc_dialog_list_padding_bottom_no_buttons = 2131099680;
        public static int abc_dialog_list_padding_top_no_title = 2131099681;
        public static int abc_dialog_min_width_major = 2131099682;
        public static int abc_dialog_min_width_minor = 2131099683;
        public static int abc_dialog_padding_material = 2131099684;
        public static int abc_dialog_padding_top_material = 2131099685;
        public static int abc_dialog_title_divider_material = 2131099686;
        public static int abc_disabled_alpha_material_dark = 2131099687;
        public static int abc_disabled_alpha_material_light = 2131099688;
        public static int abc_dropdownitem_icon_width = 2131099689;
        public static int abc_dropdownitem_text_padding_left = 2131099690;
        public static int abc_dropdownitem_text_padding_right = 2131099691;
        public static int abc_edit_text_inset_bottom_material = 2131099692;
        public static int abc_edit_text_inset_horizontal_material = 2131099693;
        public static int abc_edit_text_inset_top_material = 2131099694;
        public static int abc_floating_window_z = 2131099695;
        public static int abc_list_item_height_large_material = 2131099696;
        public static int abc_list_item_height_material = 2131099697;
        public static int abc_list_item_height_small_material = 2131099698;
        public static int abc_list_item_padding_horizontal_material = 2131099699;
        public static int abc_panel_menu_list_width = 2131099700;
        public static int abc_progress_bar_height_material = 2131099701;
        public static int abc_search_view_preferred_height = 2131099702;
        public static int abc_search_view_preferred_width = 2131099703;
        public static int abc_seekbar_track_background_height_material = 2131099704;
        public static int abc_seekbar_track_progress_height_material = 2131099705;
        public static int abc_select_dialog_padding_start_material = 2131099706;
        public static int abc_star_big = 2131099707;
        public static int abc_star_medium = 2131099708;
        public static int abc_star_small = 2131099709;
        public static int abc_switch_padding = 2131099710;
        public static int abc_text_size_body_1_material = 2131099711;
        public static int abc_text_size_body_2_material = 2131099712;
        public static int abc_text_size_button_material = 2131099713;
        public static int abc_text_size_caption_material = 2131099714;
        public static int abc_text_size_display_1_material = 2131099715;
        public static int abc_text_size_display_2_material = 2131099716;
        public static int abc_text_size_display_3_material = 2131099717;
        public static int abc_text_size_display_4_material = 2131099718;
        public static int abc_text_size_headline_material = 2131099719;
        public static int abc_text_size_large_material = 2131099720;
        public static int abc_text_size_medium_material = 2131099721;
        public static int abc_text_size_menu_header_material = 2131099722;
        public static int abc_text_size_menu_material = 2131099723;
        public static int abc_text_size_small_material = 2131099724;
        public static int abc_text_size_subhead_material = 2131099725;
        public static int abc_text_size_subtitle_material_toolbar = 2131099726;
        public static int abc_text_size_title_material = 2131099727;
        public static int abc_text_size_title_material_toolbar = 2131099728;
        public static int disabled_alpha_material_dark = 2131099792;
        public static int disabled_alpha_material_light = 2131099793;
        public static int highlight_alpha_material_colored = 2131099804;
        public static int highlight_alpha_material_dark = 2131099805;
        public static int highlight_alpha_material_light = 2131099806;
        public static int hint_alpha_material_dark = 2131099807;
        public static int hint_alpha_material_light = 2131099808;
        public static int hint_pressed_alpha_material_dark = 2131099809;
        public static int hint_pressed_alpha_material_light = 2131099810;
        public static int tooltip_corner_radius = 2131100460;
        public static int tooltip_horizontal_padding = 2131100461;
        public static int tooltip_margin = 2131100462;
        public static int tooltip_precise_anchor_extra_offset = 2131100463;
        public static int tooltip_precise_anchor_threshold = 2131100464;
        public static int tooltip_vertical_padding = 2131100465;
        public static int tooltip_y_offset_non_touch = 2131100466;
        public static int tooltip_y_offset_touch = 2131100467;

        private dimen() {
        }
    }

    /* renamed from: androidx.appcompat.R$drawable */
    public static final class drawable {
        public static int abc_ab_share_pack_mtrl_alpha = 2131165225;
        public static int abc_action_bar_item_background_material = 2131165226;
        public static int abc_btn_borderless_material = 2131165227;
        public static int abc_btn_check_material = 2131165228;
        public static int abc_btn_check_material_anim = 2131165229;
        public static int abc_btn_check_to_on_mtrl_000 = 2131165230;
        public static int abc_btn_check_to_on_mtrl_015 = 2131165231;
        public static int abc_btn_colored_material = 2131165232;
        public static int abc_btn_default_mtrl_shape = 2131165233;
        public static int abc_btn_radio_material = 2131165234;
        public static int abc_btn_radio_material_anim = 2131165235;
        public static int abc_btn_radio_to_on_mtrl_000 = 2131165236;
        public static int abc_btn_radio_to_on_mtrl_015 = 2131165237;
        public static int abc_btn_switch_to_on_mtrl_00001 = 2131165238;
        public static int abc_btn_switch_to_on_mtrl_00012 = 2131165239;
        public static int abc_cab_background_internal_bg = 2131165240;
        public static int abc_cab_background_top_material = 2131165241;
        public static int abc_cab_background_top_mtrl_alpha = 2131165242;
        public static int abc_control_background_material = 2131165243;
        public static int abc_dialog_material_background = 2131165244;
        public static int abc_edit_text_material = 2131165245;
        public static int abc_ic_ab_back_material = 2131165246;
        public static int abc_ic_arrow_drop_right_black_24dp = 2131165247;
        public static int abc_ic_clear_material = 2131165248;
        public static int abc_ic_commit_search_api_mtrl_alpha = 2131165249;
        public static int abc_ic_go_search_api_material = 2131165250;
        public static int abc_ic_menu_copy_mtrl_am_alpha = 2131165251;
        public static int abc_ic_menu_cut_mtrl_alpha = 2131165252;
        public static int abc_ic_menu_overflow_material = 2131165253;
        public static int abc_ic_menu_paste_mtrl_am_alpha = 2131165254;
        public static int abc_ic_menu_selectall_mtrl_alpha = 2131165255;
        public static int abc_ic_menu_share_mtrl_alpha = 2131165256;
        public static int abc_ic_search_api_material = 2131165257;
        public static int abc_ic_voice_search_api_material = 2131165258;
        public static int abc_item_background_holo_dark = 2131165259;
        public static int abc_item_background_holo_light = 2131165260;
        public static int abc_list_divider_material = 2131165261;
        public static int abc_list_divider_mtrl_alpha = 2131165262;
        public static int abc_list_focused_holo = 2131165263;
        public static int abc_list_longpressed_holo = 2131165264;
        public static int abc_list_pressed_holo_dark = 2131165265;
        public static int abc_list_pressed_holo_light = 2131165266;
        public static int abc_list_selector_background_transition_holo_dark = 2131165267;
        public static int abc_list_selector_background_transition_holo_light = 2131165268;
        public static int abc_list_selector_disabled_holo_dark = 2131165269;
        public static int abc_list_selector_disabled_holo_light = 2131165270;
        public static int abc_list_selector_holo_dark = 2131165271;
        public static int abc_list_selector_holo_light = 2131165272;
        public static int abc_menu_hardkey_panel_mtrl_mult = 2131165273;
        public static int abc_popup_background_mtrl_mult = 2131165274;
        public static int abc_ratingbar_indicator_material = 2131165275;
        public static int abc_ratingbar_material = 2131165276;
        public static int abc_ratingbar_small_material = 2131165277;
        public static int abc_scrubber_control_off_mtrl_alpha = 2131165278;
        public static int abc_scrubber_control_to_pressed_mtrl_000 = 2131165279;
        public static int abc_scrubber_control_to_pressed_mtrl_005 = 2131165280;
        public static int abc_scrubber_primary_mtrl_alpha = 2131165281;
        public static int abc_scrubber_track_mtrl_alpha = 2131165282;
        public static int abc_seekbar_thumb_material = 2131165283;
        public static int abc_seekbar_tick_mark_material = 2131165284;
        public static int abc_seekbar_track_material = 2131165285;
        public static int abc_spinner_mtrl_am_alpha = 2131165286;
        public static int abc_spinner_textfield_background_material = 2131165287;
        public static int abc_star_black_48dp = 2131165288;
        public static int abc_star_half_black_48dp = 2131165289;
        public static int abc_switch_thumb_material = 2131165290;
        public static int abc_switch_track_mtrl_alpha = 2131165291;
        public static int abc_tab_indicator_material = 2131165292;
        public static int abc_tab_indicator_mtrl_alpha = 2131165293;
        public static int abc_text_cursor_material = 2131165294;
        public static int abc_text_select_handle_left_mtrl = 2131165295;
        public static int abc_text_select_handle_middle_mtrl = 2131165296;
        public static int abc_text_select_handle_right_mtrl = 2131165297;
        public static int abc_textfield_activated_mtrl_alpha = 2131165298;
        public static int abc_textfield_default_mtrl_alpha = 2131165299;
        public static int abc_textfield_search_activated_mtrl_alpha = 2131165300;
        public static int abc_textfield_search_default_mtrl_alpha = 2131165301;
        public static int abc_textfield_search_material = 2131165302;
        public static int btn_checkbox_checked_mtrl = 2131165306;
        public static int btn_checkbox_checked_to_unchecked_mtrl_animation = 2131165307;
        public static int btn_checkbox_unchecked_mtrl = 2131165308;
        public static int btn_checkbox_unchecked_to_checked_mtrl_animation = 2131165309;
        public static int btn_radio_off_mtrl = 2131165310;
        public static int btn_radio_off_to_on_mtrl_animation = 2131165311;
        public static int btn_radio_on_mtrl = 2131165312;
        public static int btn_radio_on_to_off_mtrl_animation = 2131165313;
        public static int test_level_drawable = 2131165439;
        public static int tooltip_frame_dark = 2131165440;
        public static int tooltip_frame_light = 2131165441;

        private drawable() {
        }
    }

    /* renamed from: androidx.appcompat.R$id */
    public static final class id {
        public static int action_bar = 2131230771;
        public static int action_bar_activity_content = 2131230772;
        public static int action_bar_container = 2131230773;
        public static int action_bar_root = 2131230774;
        public static int action_bar_spinner = 2131230775;
        public static int action_bar_subtitle = 2131230776;
        public static int action_bar_title = 2131230777;
        public static int action_context_bar = 2131230779;
        public static int action_menu_divider = 2131230782;
        public static int action_menu_presenter = 2131230783;
        public static int action_mode_bar = 2131230784;
        public static int action_mode_bar_stub = 2131230785;
        public static int action_mode_close_button = 2131230786;
        public static int activity_chooser_view_content = 2131230789;
        public static int add = 2131230790;
        public static int alertTitle = 2131230791;
        public static int buttonPanel = 2131230821;
        public static int checkbox = 2131230836;
        public static int checked = 2131230837;
        public static int content = 2131230850;
        public static int contentPanel = 2131230851;
        public static int custom = 2131230859;
        public static int customPanel = 2131230860;
        public static int decor_content_parent = 2131230865;
        public static int default_activity_button = 2131230866;
        public static int edit_query = 2131230903;
        public static int expand_activities_button = 2131230913;
        public static int expanded_menu = 2131230914;
        public static int group_divider = 2131230937;
        public static int home = 2131230943;
        public static int icon = 2131230947;
        public static int image = 2131230952;
        public static int listMode = 2131230982;
        public static int list_item = 2131230984;
        public static int message = 2131231014;
        public static int multiply = 2131231048;
        public static int none = 2131231068;
        public static int normal = 2131231069;
        public static int off = 2131231074;

        /* renamed from: on */
        public static int f6on = 2131231075;
        public static int parentPanel = 2131231097;
        public static int progress_circular = 2131231111;
        public static int progress_horizontal = 2131231112;
        public static int radio = 2131231113;
        public static int screen = 2131231129;
        public static int scrollIndicatorDown = 2131231131;
        public static int scrollIndicatorUp = 2131231132;
        public static int scrollView = 2131231133;
        public static int search_badge = 2131231135;
        public static int search_bar = 2131231136;
        public static int search_button = 2131231137;
        public static int search_close_btn = 2131231138;
        public static int search_edit_frame = 2131231139;
        public static int search_go_btn = 2131231140;
        public static int search_mag_icon = 2131231141;
        public static int search_plate = 2131231142;
        public static int search_src_text = 2131231143;
        public static int search_voice_btn = 2131231144;
        public static int select_dialog_listview = 2131231146;
        public static int shortcut = 2131231151;
        public static int spacer = 2131231166;
        public static int split_action_bar = 2131231171;
        public static int src_atop = 2131231176;
        public static int src_in = 2131231177;
        public static int src_over = 2131231178;
        public static int submenuarrow = 2131231188;
        public static int submit_area = 2131231189;
        public static int tabMode = 2131231192;
        public static int textSpacerNoButtons = 2131231209;
        public static int textSpacerNoTitle = 2131231210;
        public static int title = 2131231244;
        public static int titleDividerNoCustom = 2131231245;
        public static int title_template = 2131231246;
        public static int topPanel = 2131231249;
        public static int unchecked = 2131231262;
        public static int uniform = 2131231263;

        /* renamed from: up */
        public static int f7up = 2131231265;
        public static int wrap_content = 2131231287;

        private id() {
        }
    }

    /* renamed from: androidx.appcompat.R$integer */
    public static final class integer {
        public static int abc_config_activityDefaultDur = 2131296256;
        public static int abc_config_activityShortDur = 2131296257;
        public static int cancel_button_image_alpha = 2131296260;
        public static int config_tooltipAnimTime = 2131296262;

        private integer() {
        }
    }

    /* renamed from: androidx.appcompat.R$interpolator */
    public static final class interpolator {
        public static int btn_checkbox_checked_mtrl_animation_interpolator_0 = 2131361792;
        public static int btn_checkbox_checked_mtrl_animation_interpolator_1 = 2131361793;
        public static int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 2131361794;
        public static int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 2131361795;
        public static int btn_radio_to_off_mtrl_animation_interpolator_0 = 2131361796;
        public static int btn_radio_to_on_mtrl_animation_interpolator_0 = 2131361797;
        public static int fast_out_slow_in = 2131361798;

        private interpolator() {
        }
    }

    /* renamed from: androidx.appcompat.R$layout */
    public static final class layout {
        public static int abc_action_bar_title_item = 2131427328;
        public static int abc_action_bar_up_container = 2131427329;
        public static int abc_action_menu_item_layout = 2131427330;
        public static int abc_action_menu_layout = 2131427331;
        public static int abc_action_mode_bar = 2131427332;
        public static int abc_action_mode_close_item_material = 2131427333;
        public static int abc_activity_chooser_view = 2131427334;
        public static int abc_activity_chooser_view_list_item = 2131427335;
        public static int abc_alert_dialog_button_bar_material = 2131427336;
        public static int abc_alert_dialog_material = 2131427337;
        public static int abc_alert_dialog_title_material = 2131427338;
        public static int abc_cascading_menu_item_layout = 2131427339;
        public static int abc_dialog_title_material = 2131427340;
        public static int abc_expanded_menu_layout = 2131427341;
        public static int abc_list_menu_item_checkbox = 2131427342;
        public static int abc_list_menu_item_icon = 2131427343;
        public static int abc_list_menu_item_layout = 2131427344;
        public static int abc_list_menu_item_radio = 2131427345;
        public static int abc_popup_menu_header_item_layout = 2131427346;
        public static int abc_popup_menu_item_layout = 2131427347;
        public static int abc_screen_content_include = 2131427348;
        public static int abc_screen_simple = 2131427349;
        public static int abc_screen_simple_overlay_action_mode = 2131427350;
        public static int abc_screen_toolbar = 2131427351;
        public static int abc_search_dropdown_item_icons_2line = 2131427352;
        public static int abc_search_view = 2131427353;
        public static int abc_select_dialog_material = 2131427354;
        public static int abc_tooltip = 2131427355;
        public static int select_dialog_item_material = 2131427445;
        public static int select_dialog_multichoice_material = 2131427446;
        public static int select_dialog_singlechoice_material = 2131427447;
        public static int support_simple_spinner_dropdown_item = 2131427449;

        private layout() {
        }
    }

    /* renamed from: androidx.appcompat.R$string */
    public static final class string {
        public static int abc_action_bar_home_description = 2131820544;
        public static int abc_action_bar_up_description = 2131820545;
        public static int abc_action_menu_overflow_description = 2131820546;
        public static int abc_action_mode_done = 2131820547;
        public static int abc_activity_chooser_view_see_all = 2131820548;
        public static int abc_activitychooserview_choose_application = 2131820549;
        public static int abc_capital_off = 2131820550;
        public static int abc_capital_on = 2131820551;
        public static int abc_menu_alt_shortcut_label = 2131820552;
        public static int abc_menu_ctrl_shortcut_label = 2131820553;
        public static int abc_menu_delete_shortcut_label = 2131820554;
        public static int abc_menu_enter_shortcut_label = 2131820555;
        public static int abc_menu_function_shortcut_label = 2131820556;
        public static int abc_menu_meta_shortcut_label = 2131820557;
        public static int abc_menu_shift_shortcut_label = 2131820558;
        public static int abc_menu_space_shortcut_label = 2131820559;
        public static int abc_menu_sym_shortcut_label = 2131820560;
        public static int abc_prepend_shortcut_label = **********;
        public static int abc_search_hint = **********;
        public static int abc_searchview_description_clear = **********;
        public static int abc_searchview_description_query = **********;
        public static int abc_searchview_description_search = **********;
        public static int abc_searchview_description_submit = **********;
        public static int abc_searchview_description_voice = **********;
        public static int abc_shareactionprovider_share_with = **********;
        public static int abc_shareactionprovider_share_with_application = **********;
        public static int abc_toolbar_collapse_description = **********;
        public static int search_menu_title = **********;

        private string() {
        }
    }

    /* renamed from: androidx.appcompat.R$style */
    public static final class style {
        public static int AlertDialog_AppCompat = **********;
        public static int AlertDialog_AppCompat_Light = **********;
        public static int Animation_AppCompat_Dialog = **********;
        public static int Animation_AppCompat_DropDownUp = **********;
        public static int Animation_AppCompat_Tooltip = **********;
        public static int Base_AlertDialog_AppCompat = **********;
        public static int Base_AlertDialog_AppCompat_Light = **********;
        public static int Base_Animation_AppCompat_Dialog = **********;
        public static int Base_Animation_AppCompat_DropDownUp = **********;
        public static int Base_Animation_AppCompat_Tooltip = 2131886095;
        public static int Base_DialogWindowTitleBackground_AppCompat = 2131886098;
        public static int Base_DialogWindowTitle_AppCompat = 2131886097;
        public static int Base_TextAppearance_AppCompat = 2131886102;
        public static int Base_TextAppearance_AppCompat_Body1 = 2131886103;
        public static int Base_TextAppearance_AppCompat_Body2 = 2131886104;
        public static int Base_TextAppearance_AppCompat_Button = 2131886105;
        public static int Base_TextAppearance_AppCompat_Caption = 2131886106;
        public static int Base_TextAppearance_AppCompat_Display1 = 2131886107;
        public static int Base_TextAppearance_AppCompat_Display2 = 2131886108;
        public static int Base_TextAppearance_AppCompat_Display3 = 2131886109;
        public static int Base_TextAppearance_AppCompat_Display4 = 2131886110;
        public static int Base_TextAppearance_AppCompat_Headline = 2131886111;
        public static int Base_TextAppearance_AppCompat_Inverse = 2131886112;
        public static int Base_TextAppearance_AppCompat_Large = 2131886113;
        public static int Base_TextAppearance_AppCompat_Large_Inverse = 2131886114;
        public static int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 2131886115;
        public static int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 2131886116;
        public static int Base_TextAppearance_AppCompat_Medium = 2131886117;
        public static int Base_TextAppearance_AppCompat_Medium_Inverse = 2131886118;
        public static int Base_TextAppearance_AppCompat_Menu = 2131886119;
        public static int Base_TextAppearance_AppCompat_SearchResult = 2131886120;
        public static int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 2131886121;
        public static int Base_TextAppearance_AppCompat_SearchResult_Title = 2131886122;
        public static int Base_TextAppearance_AppCompat_Small = 2131886123;
        public static int Base_TextAppearance_AppCompat_Small_Inverse = 2131886124;
        public static int Base_TextAppearance_AppCompat_Subhead = 2131886125;
        public static int Base_TextAppearance_AppCompat_Subhead_Inverse = 2131886126;
        public static int Base_TextAppearance_AppCompat_Title = 2131886127;
        public static int Base_TextAppearance_AppCompat_Title_Inverse = 2131886128;
        public static int Base_TextAppearance_AppCompat_Tooltip = 2131886129;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 2131886130;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 2131886131;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 2131886132;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 2131886133;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 2131886134;
        public static int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 2131886135;
        public static int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 2131886136;
        public static int Base_TextAppearance_AppCompat_Widget_Button = 2131886137;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 2131886138;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Colored = 2131886139;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 2131886140;
        public static int Base_TextAppearance_AppCompat_Widget_DropDownItem = 2131886141;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 2131886142;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 2131886143;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 2131886144;
        public static int Base_TextAppearance_AppCompat_Widget_Switch = 2131886145;
        public static int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 2131886146;
        public static int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 2131886152;
        public static int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 2131886153;
        public static int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 2131886154;
        public static int Base_ThemeOverlay_AppCompat = 2131886203;
        public static int Base_ThemeOverlay_AppCompat_ActionBar = 2131886204;
        public static int Base_ThemeOverlay_AppCompat_Dark = 2131886205;
        public static int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 2131886206;
        public static int Base_ThemeOverlay_AppCompat_Dialog = 2131886207;
        public static int Base_ThemeOverlay_AppCompat_Dialog_Alert = 2131886208;
        public static int Base_ThemeOverlay_AppCompat_Light = 2131886209;
        public static int Base_Theme_AppCompat = 2131886155;
        public static int Base_Theme_AppCompat_CompactMenu = 2131886156;
        public static int Base_Theme_AppCompat_Dialog = 2131886157;
        public static int Base_Theme_AppCompat_DialogWhenLarge = 2131886161;
        public static int Base_Theme_AppCompat_Dialog_Alert = 2131886158;
        public static int Base_Theme_AppCompat_Dialog_FixedSize = 2131886159;
        public static int Base_Theme_AppCompat_Dialog_MinWidth = 2131886160;
        public static int Base_Theme_AppCompat_Light = 2131886162;
        public static int Base_Theme_AppCompat_Light_DarkActionBar = 2131886163;
        public static int Base_Theme_AppCompat_Light_Dialog = 2131886164;
        public static int Base_Theme_AppCompat_Light_DialogWhenLarge = 2131886168;
        public static int Base_Theme_AppCompat_Light_Dialog_Alert = 2131886165;
        public static int Base_Theme_AppCompat_Light_Dialog_FixedSize = 2131886166;
        public static int Base_Theme_AppCompat_Light_Dialog_MinWidth = 2131886167;
        public static int Base_V21_ThemeOverlay_AppCompat_Dialog = 2131886252;
        public static int Base_V21_Theme_AppCompat = 2131886244;
        public static int Base_V21_Theme_AppCompat_Dialog = 2131886245;
        public static int Base_V21_Theme_AppCompat_Light = 2131886246;
        public static int Base_V21_Theme_AppCompat_Light_Dialog = 2131886247;
        public static int Base_V22_Theme_AppCompat = 2131886256;
        public static int Base_V22_Theme_AppCompat_Light = 2131886257;
        public static int Base_V23_Theme_AppCompat = 2131886258;
        public static int Base_V23_Theme_AppCompat_Light = 2131886259;
        public static int Base_V26_Theme_AppCompat = 2131886264;
        public static int Base_V26_Theme_AppCompat_Light = 2131886265;
        public static int Base_V26_Widget_AppCompat_Toolbar = 2131886266;
        public static int Base_V28_Theme_AppCompat = 2131886267;
        public static int Base_V28_Theme_AppCompat_Light = 2131886268;
        public static int Base_V7_ThemeOverlay_AppCompat_Dialog = 2131886273;
        public static int Base_V7_Theme_AppCompat = 2131886269;
        public static int Base_V7_Theme_AppCompat_Dialog = 2131886270;
        public static int Base_V7_Theme_AppCompat_Light = 2131886271;
        public static int Base_V7_Theme_AppCompat_Light_Dialog = 2131886272;
        public static int Base_V7_Widget_AppCompat_AutoCompleteTextView = 2131886274;
        public static int Base_V7_Widget_AppCompat_EditText = 2131886275;
        public static int Base_V7_Widget_AppCompat_Toolbar = 2131886276;
        public static int Base_Widget_AppCompat_ActionBar = 2131886277;
        public static int Base_Widget_AppCompat_ActionBar_Solid = 2131886278;
        public static int Base_Widget_AppCompat_ActionBar_TabBar = 2131886279;
        public static int Base_Widget_AppCompat_ActionBar_TabText = 2131886280;
        public static int Base_Widget_AppCompat_ActionBar_TabView = 2131886281;
        public static int Base_Widget_AppCompat_ActionButton = 2131886282;
        public static int Base_Widget_AppCompat_ActionButton_CloseMode = 2131886283;
        public static int Base_Widget_AppCompat_ActionButton_Overflow = 2131886284;
        public static int Base_Widget_AppCompat_ActionMode = 2131886285;
        public static int Base_Widget_AppCompat_ActivityChooserView = 2131886286;
        public static int Base_Widget_AppCompat_AutoCompleteTextView = 2131886287;
        public static int Base_Widget_AppCompat_Button = 2131886288;
        public static int Base_Widget_AppCompat_ButtonBar = 2131886294;
        public static int Base_Widget_AppCompat_ButtonBar_AlertDialog = 2131886295;
        public static int Base_Widget_AppCompat_Button_Borderless = 2131886289;
        public static int Base_Widget_AppCompat_Button_Borderless_Colored = 2131886290;
        public static int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 2131886291;
        public static int Base_Widget_AppCompat_Button_Colored = 2131886292;
        public static int Base_Widget_AppCompat_Button_Small = 2131886293;
        public static int Base_Widget_AppCompat_CompoundButton_CheckBox = 2131886296;
        public static int Base_Widget_AppCompat_CompoundButton_RadioButton = 2131886297;
        public static int Base_Widget_AppCompat_CompoundButton_Switch = 2131886298;
        public static int Base_Widget_AppCompat_DrawerArrowToggle = 2131886299;
        public static int Base_Widget_AppCompat_DrawerArrowToggle_Common = 2131886300;
        public static int Base_Widget_AppCompat_DropDownItem_Spinner = 2131886301;
        public static int Base_Widget_AppCompat_EditText = 2131886302;
        public static int Base_Widget_AppCompat_ImageButton = 2131886303;
        public static int Base_Widget_AppCompat_Light_ActionBar = 2131886304;
        public static int Base_Widget_AppCompat_Light_ActionBar_Solid = 2131886305;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabBar = 2131886306;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabText = 2131886307;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 2131886308;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabView = 2131886309;
        public static int Base_Widget_AppCompat_Light_PopupMenu = 2131886310;
        public static int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 2131886311;
        public static int Base_Widget_AppCompat_ListMenuView = 2131886312;
        public static int Base_Widget_AppCompat_ListPopupWindow = 2131886313;
        public static int Base_Widget_AppCompat_ListView = 2131886314;
        public static int Base_Widget_AppCompat_ListView_DropDown = 2131886315;
        public static int Base_Widget_AppCompat_ListView_Menu = 2131886316;
        public static int Base_Widget_AppCompat_PopupMenu = 2131886317;
        public static int Base_Widget_AppCompat_PopupMenu_Overflow = 2131886318;
        public static int Base_Widget_AppCompat_PopupWindow = 2131886319;
        public static int Base_Widget_AppCompat_ProgressBar = 2131886320;
        public static int Base_Widget_AppCompat_ProgressBar_Horizontal = 2131886321;
        public static int Base_Widget_AppCompat_RatingBar = 2131886322;
        public static int Base_Widget_AppCompat_RatingBar_Indicator = 2131886323;
        public static int Base_Widget_AppCompat_RatingBar_Small = 2131886324;
        public static int Base_Widget_AppCompat_SearchView = 2131886325;
        public static int Base_Widget_AppCompat_SearchView_ActionBar = 2131886326;
        public static int Base_Widget_AppCompat_SeekBar = 2131886327;
        public static int Base_Widget_AppCompat_SeekBar_Discrete = 2131886328;
        public static int Base_Widget_AppCompat_Spinner = 2131886329;
        public static int Base_Widget_AppCompat_Spinner_Underlined = 2131886330;
        public static int Base_Widget_AppCompat_TextView = 2131886331;
        public static int Base_Widget_AppCompat_TextView_SpinnerItem = 2131886332;
        public static int Base_Widget_AppCompat_Toolbar = 2131886333;
        public static int Base_Widget_AppCompat_Toolbar_Button_Navigation = 2131886334;
        public static int Platform_AppCompat = 2131886398;
        public static int Platform_AppCompat_Light = 2131886399;
        public static int Platform_ThemeOverlay_AppCompat = 2131886404;
        public static int Platform_ThemeOverlay_AppCompat_Dark = 2131886405;
        public static int Platform_ThemeOverlay_AppCompat_Light = 2131886406;
        public static int Platform_V21_AppCompat = 2131886407;
        public static int Platform_V21_AppCompat_Light = 2131886408;
        public static int Platform_V25_AppCompat = 2131886409;
        public static int Platform_V25_AppCompat_Light = 2131886410;
        public static int Platform_Widget_AppCompat_Spinner = 2131886411;
        public static int RtlOverlay_DialogWindowTitle_AppCompat = 2131886412;
        public static int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 2131886413;
        public static int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 2131886414;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem = 2131886415;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 2131886416;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 2131886417;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 2131886418;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 2131886419;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 2131886420;
        public static int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 2131886426;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown = 2131886421;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 2131886422;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 2131886423;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 2131886424;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 2131886425;
        public static int RtlUnderlay_Widget_AppCompat_ActionButton = 2131886427;
        public static int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 2131886428;
        public static int TextAppearance_AppCompat = 2131886491;
        public static int TextAppearance_AppCompat_Body1 = 2131886492;
        public static int TextAppearance_AppCompat_Body2 = 2131886493;
        public static int TextAppearance_AppCompat_Button = 2131886494;
        public static int TextAppearance_AppCompat_Caption = 2131886495;
        public static int TextAppearance_AppCompat_Display1 = 2131886496;
        public static int TextAppearance_AppCompat_Display2 = 2131886497;
        public static int TextAppearance_AppCompat_Display3 = 2131886498;
        public static int TextAppearance_AppCompat_Display4 = 2131886499;
        public static int TextAppearance_AppCompat_Headline = 2131886500;
        public static int TextAppearance_AppCompat_Inverse = 2131886501;
        public static int TextAppearance_AppCompat_Large = 2131886502;
        public static int TextAppearance_AppCompat_Large_Inverse = 2131886503;
        public static int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 2131886504;
        public static int TextAppearance_AppCompat_Light_SearchResult_Title = 2131886505;
        public static int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 2131886506;
        public static int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 2131886507;
        public static int TextAppearance_AppCompat_Medium = 2131886508;
        public static int TextAppearance_AppCompat_Medium_Inverse = 2131886509;
        public static int TextAppearance_AppCompat_Menu = 2131886510;
        public static int TextAppearance_AppCompat_SearchResult_Subtitle = 2131886511;
        public static int TextAppearance_AppCompat_SearchResult_Title = 2131886512;
        public static int TextAppearance_AppCompat_Small = 2131886513;
        public static int TextAppearance_AppCompat_Small_Inverse = 2131886514;
        public static int TextAppearance_AppCompat_Subhead = 2131886515;
        public static int TextAppearance_AppCompat_Subhead_Inverse = 2131886516;
        public static int TextAppearance_AppCompat_Title = 2131886517;
        public static int TextAppearance_AppCompat_Title_Inverse = 2131886518;
        public static int TextAppearance_AppCompat_Tooltip = 2131886519;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Menu = 2131886520;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 2131886521;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 2131886522;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Title = 2131886523;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 2131886524;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 2131886525;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 2131886526;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Title = 2131886527;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 2131886528;
        public static int TextAppearance_AppCompat_Widget_Button = 2131886529;
        public static int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 2131886530;
        public static int TextAppearance_AppCompat_Widget_Button_Colored = 2131886531;
        public static int TextAppearance_AppCompat_Widget_Button_Inverse = 2131886532;
        public static int TextAppearance_AppCompat_Widget_DropDownItem = 2131886533;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Header = 2131886534;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Large = 2131886535;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Small = 2131886536;
        public static int TextAppearance_AppCompat_Widget_Switch = 2131886537;
        public static int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 2131886538;
        public static int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 2131886608;
        public static int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 2131886609;
        public static int TextAppearance_Widget_AppCompat_Toolbar_Title = 2131886610;
        public static int ThemeOverlay_AppCompat = 2131886721;
        public static int ThemeOverlay_AppCompat_ActionBar = 2131886722;
        public static int ThemeOverlay_AppCompat_Dark = 2131886723;
        public static int ThemeOverlay_AppCompat_Dark_ActionBar = 2131886724;
        public static int ThemeOverlay_AppCompat_DayNight = 2131886725;
        public static int ThemeOverlay_AppCompat_DayNight_ActionBar = 2131886726;
        public static int ThemeOverlay_AppCompat_Dialog = 2131886727;
        public static int ThemeOverlay_AppCompat_Dialog_Alert = 2131886728;
        public static int ThemeOverlay_AppCompat_Light = 2131886729;
        public static int Theme_AppCompat = 2131886613;
        public static int Theme_AppCompat_CompactMenu = 2131886614;
        public static int Theme_AppCompat_DayNight = 2131886615;
        public static int Theme_AppCompat_DayNight_DarkActionBar = 2131886616;
        public static int Theme_AppCompat_DayNight_Dialog = 2131886617;
        public static int Theme_AppCompat_DayNight_DialogWhenLarge = 2131886620;
        public static int Theme_AppCompat_DayNight_Dialog_Alert = 2131886618;
        public static int Theme_AppCompat_DayNight_Dialog_MinWidth = 2131886619;
        public static int Theme_AppCompat_DayNight_NoActionBar = 2131886621;
        public static int Theme_AppCompat_Dialog = 2131886622;
        public static int Theme_AppCompat_DialogWhenLarge = 2131886625;
        public static int Theme_AppCompat_Dialog_Alert = 2131886623;
        public static int Theme_AppCompat_Dialog_MinWidth = 2131886624;
        public static int Theme_AppCompat_Empty = 2131886626;
        public static int Theme_AppCompat_Light = 2131886627;
        public static int Theme_AppCompat_Light_DarkActionBar = 2131886628;
        public static int Theme_AppCompat_Light_Dialog = 2131886629;
        public static int Theme_AppCompat_Light_DialogWhenLarge = 2131886632;
        public static int Theme_AppCompat_Light_Dialog_Alert = 2131886630;
        public static int Theme_AppCompat_Light_Dialog_MinWidth = 2131886631;
        public static int Theme_AppCompat_Light_NoActionBar = 2131886633;
        public static int Theme_AppCompat_NoActionBar = 2131886634;
        public static int Widget_AppCompat_ActionBar = 2131886835;
        public static int Widget_AppCompat_ActionBar_Solid = 2131886836;
        public static int Widget_AppCompat_ActionBar_TabBar = 2131886837;
        public static int Widget_AppCompat_ActionBar_TabText = 2131886838;
        public static int Widget_AppCompat_ActionBar_TabView = 2131886839;
        public static int Widget_AppCompat_ActionButton = 2131886840;
        public static int Widget_AppCompat_ActionButton_CloseMode = 2131886841;
        public static int Widget_AppCompat_ActionButton_Overflow = 2131886842;
        public static int Widget_AppCompat_ActionMode = 2131886843;
        public static int Widget_AppCompat_ActivityChooserView = 2131886844;
        public static int Widget_AppCompat_AutoCompleteTextView = 2131886845;
        public static int Widget_AppCompat_Button = 2131886846;
        public static int Widget_AppCompat_ButtonBar = 2131886852;
        public static int Widget_AppCompat_ButtonBar_AlertDialog = 2131886853;
        public static int Widget_AppCompat_Button_Borderless = 2131886847;
        public static int Widget_AppCompat_Button_Borderless_Colored = 2131886848;
        public static int Widget_AppCompat_Button_ButtonBar_AlertDialog = 2131886849;
        public static int Widget_AppCompat_Button_Colored = 2131886850;
        public static int Widget_AppCompat_Button_Small = 2131886851;
        public static int Widget_AppCompat_CompoundButton_CheckBox = 2131886854;
        public static int Widget_AppCompat_CompoundButton_RadioButton = 2131886855;
        public static int Widget_AppCompat_CompoundButton_Switch = 2131886856;
        public static int Widget_AppCompat_DrawerArrowToggle = 2131886857;
        public static int Widget_AppCompat_DropDownItem_Spinner = 2131886858;
        public static int Widget_AppCompat_EditText = 2131886859;
        public static int Widget_AppCompat_ImageButton = 2131886860;
        public static int Widget_AppCompat_Light_ActionBar = 2131886861;
        public static int Widget_AppCompat_Light_ActionBar_Solid = 2131886862;
        public static int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 2131886863;
        public static int Widget_AppCompat_Light_ActionBar_TabBar = 2131886864;
        public static int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 2131886865;
        public static int Widget_AppCompat_Light_ActionBar_TabText = 2131886866;
        public static int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 2131886867;
        public static int Widget_AppCompat_Light_ActionBar_TabView = 2131886868;
        public static int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 2131886869;
        public static int Widget_AppCompat_Light_ActionButton = 2131886870;
        public static int Widget_AppCompat_Light_ActionButton_CloseMode = 2131886871;
        public static int Widget_AppCompat_Light_ActionButton_Overflow = 2131886872;
        public static int Widget_AppCompat_Light_ActionMode_Inverse = 2131886873;
        public static int Widget_AppCompat_Light_ActivityChooserView = 2131886874;
        public static int Widget_AppCompat_Light_AutoCompleteTextView = 2131886875;
        public static int Widget_AppCompat_Light_DropDownItem_Spinner = 2131886876;
        public static int Widget_AppCompat_Light_ListPopupWindow = 2131886877;
        public static int Widget_AppCompat_Light_ListView_DropDown = 2131886878;
        public static int Widget_AppCompat_Light_PopupMenu = 2131886879;
        public static int Widget_AppCompat_Light_PopupMenu_Overflow = 2131886880;
        public static int Widget_AppCompat_Light_SearchView = 2131886881;
        public static int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 2131886882;
        public static int Widget_AppCompat_ListMenuView = 2131886883;
        public static int Widget_AppCompat_ListPopupWindow = 2131886884;
        public static int Widget_AppCompat_ListView = 2131886885;
        public static int Widget_AppCompat_ListView_DropDown = 2131886886;
        public static int Widget_AppCompat_ListView_Menu = 2131886887;
        public static int Widget_AppCompat_PopupMenu = 2131886888;
        public static int Widget_AppCompat_PopupMenu_Overflow = 2131886889;
        public static int Widget_AppCompat_PopupWindow = 2131886890;
        public static int Widget_AppCompat_ProgressBar = 2131886891;
        public static int Widget_AppCompat_ProgressBar_Horizontal = 2131886892;
        public static int Widget_AppCompat_RatingBar = 2131886893;
        public static int Widget_AppCompat_RatingBar_Indicator = 2131886894;
        public static int Widget_AppCompat_RatingBar_Small = 2131886895;
        public static int Widget_AppCompat_SearchView = 2131886896;
        public static int Widget_AppCompat_SearchView_ActionBar = 2131886897;
        public static int Widget_AppCompat_SeekBar = 2131886898;
        public static int Widget_AppCompat_SeekBar_Discrete = 2131886899;
        public static int Widget_AppCompat_Spinner = 2131886900;
        public static int Widget_AppCompat_Spinner_DropDown = 2131886901;
        public static int Widget_AppCompat_Spinner_DropDown_ActionBar = 2131886902;
        public static int Widget_AppCompat_Spinner_Underlined = 2131886903;
        public static int Widget_AppCompat_TextView = 2131886904;
        public static int Widget_AppCompat_TextView_SpinnerItem = 2131886905;
        public static int Widget_AppCompat_Toolbar = 2131886906;
        public static int Widget_AppCompat_Toolbar_Button_Navigation = 2131886907;

        private style() {
        }
    }

    /* renamed from: androidx.appcompat.R$styleable */
    public static final class styleable {
        public static int ActionBarLayout_android_layout_gravity = 0;
        public static int ActionBar_background = 0;
        public static int ActionBar_backgroundSplit = 1;
        public static int ActionBar_backgroundStacked = 2;
        public static int ActionBar_contentInsetEnd = 3;
        public static int ActionBar_contentInsetEndWithActions = 4;
        public static int ActionBar_contentInsetLeft = 5;
        public static int ActionBar_contentInsetRight = 6;
        public static int ActionBar_contentInsetStart = 7;
        public static int ActionBar_contentInsetStartWithNavigation = 8;
        public static int ActionBar_customNavigationLayout = 9;
        public static int ActionBar_displayOptions = 10;
        public static int ActionBar_divider = 11;
        public static int ActionBar_elevation = 12;
        public static int ActionBar_height = 13;
        public static int ActionBar_hideOnContentScroll = 14;
        public static int ActionBar_homeAsUpIndicator = 15;
        public static int ActionBar_homeLayout = 16;
        public static int ActionBar_icon = 17;
        public static int ActionBar_indeterminateProgressStyle = 18;
        public static int ActionBar_itemPadding = 19;
        public static int ActionBar_logo = 20;
        public static int ActionBar_navigationMode = 21;
        public static int ActionBar_popupTheme = 22;
        public static int ActionBar_progressBarPadding = 23;
        public static int ActionBar_progressBarStyle = 24;
        public static int ActionBar_subtitle = 25;
        public static int ActionBar_subtitleTextStyle = 26;
        public static int ActionBar_title = 27;
        public static int ActionBar_titleTextStyle = 28;
        public static int ActionMenuItemView_android_minWidth = 0;
        public static int ActionMode_background = 0;
        public static int ActionMode_backgroundSplit = 1;
        public static int ActionMode_closeItemLayout = 2;
        public static int ActionMode_height = 3;
        public static int ActionMode_subtitleTextStyle = 4;
        public static int ActionMode_titleTextStyle = 5;
        public static int ActivityChooserView_expandActivityOverflowButtonDrawable = 0;
        public static int ActivityChooserView_initialActivityCount = 1;
        public static int AlertDialog_android_layout = 0;
        public static int AlertDialog_buttonIconDimen = 1;
        public static int AlertDialog_buttonPanelSideLayout = 2;
        public static int AlertDialog_listItemLayout = 3;
        public static int AlertDialog_listLayout = 4;
        public static int AlertDialog_multiChoiceItemLayout = 5;
        public static int AlertDialog_showTitle = 6;
        public static int AlertDialog_singleChoiceItemLayout = 7;
        public static int AppCompatImageView_android_src = 0;
        public static int AppCompatImageView_srcCompat = 1;
        public static int AppCompatImageView_tint = 2;
        public static int AppCompatImageView_tintMode = 3;
        public static int AppCompatSeekBar_android_thumb = 0;
        public static int AppCompatSeekBar_tickMark = 1;
        public static int AppCompatSeekBar_tickMarkTint = 2;
        public static int AppCompatSeekBar_tickMarkTintMode = 3;
        public static int AppCompatTextHelper_android_drawableBottom = 2;
        public static int AppCompatTextHelper_android_drawableEnd = 6;
        public static int AppCompatTextHelper_android_drawableLeft = 3;
        public static int AppCompatTextHelper_android_drawableRight = 4;
        public static int AppCompatTextHelper_android_drawableStart = 5;
        public static int AppCompatTextHelper_android_drawableTop = 1;
        public static int AppCompatTextHelper_android_textAppearance = 0;
        public static int AppCompatTextView_android_textAppearance = 0;
        public static int AppCompatTextView_autoSizeMaxTextSize = 1;
        public static int AppCompatTextView_autoSizeMinTextSize = 2;
        public static int AppCompatTextView_autoSizePresetSizes = 3;
        public static int AppCompatTextView_autoSizeStepGranularity = 4;
        public static int AppCompatTextView_autoSizeTextType = 5;
        public static int AppCompatTextView_drawableBottomCompat = 6;
        public static int AppCompatTextView_drawableEndCompat = 7;
        public static int AppCompatTextView_drawableLeftCompat = 8;
        public static int AppCompatTextView_drawableRightCompat = 9;
        public static int AppCompatTextView_drawableStartCompat = 10;
        public static int AppCompatTextView_drawableTint = 11;
        public static int AppCompatTextView_drawableTintMode = 12;
        public static int AppCompatTextView_drawableTopCompat = 13;
        public static int AppCompatTextView_emojiCompatEnabled = 14;
        public static int AppCompatTextView_firstBaselineToTopHeight = 15;
        public static int AppCompatTextView_fontFamily = 16;
        public static int AppCompatTextView_fontVariationSettings = 17;
        public static int AppCompatTextView_lastBaselineToBottomHeight = 18;
        public static int AppCompatTextView_lineHeight = 19;
        public static int AppCompatTextView_textAllCaps = 20;
        public static int AppCompatTextView_textLocale = 21;
        public static int AppCompatTheme_actionBarDivider = 2;
        public static int AppCompatTheme_actionBarItemBackground = 3;
        public static int AppCompatTheme_actionBarPopupTheme = 4;
        public static int AppCompatTheme_actionBarSize = 5;
        public static int AppCompatTheme_actionBarSplitStyle = 6;
        public static int AppCompatTheme_actionBarStyle = 7;
        public static int AppCompatTheme_actionBarTabBarStyle = 8;
        public static int AppCompatTheme_actionBarTabStyle = 9;
        public static int AppCompatTheme_actionBarTabTextStyle = 10;
        public static int AppCompatTheme_actionBarTheme = 11;
        public static int AppCompatTheme_actionBarWidgetTheme = 12;
        public static int AppCompatTheme_actionButtonStyle = 13;
        public static int AppCompatTheme_actionDropDownStyle = 14;
        public static int AppCompatTheme_actionMenuTextAppearance = 15;
        public static int AppCompatTheme_actionMenuTextColor = 16;
        public static int AppCompatTheme_actionModeBackground = 17;
        public static int AppCompatTheme_actionModeCloseButtonStyle = 18;
        public static int AppCompatTheme_actionModeCloseContentDescription = 19;
        public static int AppCompatTheme_actionModeCloseDrawable = 20;
        public static int AppCompatTheme_actionModeCopyDrawable = 21;
        public static int AppCompatTheme_actionModeCutDrawable = 22;
        public static int AppCompatTheme_actionModeFindDrawable = 23;
        public static int AppCompatTheme_actionModePasteDrawable = 24;
        public static int AppCompatTheme_actionModePopupWindowStyle = 25;
        public static int AppCompatTheme_actionModeSelectAllDrawable = 26;
        public static int AppCompatTheme_actionModeShareDrawable = 27;
        public static int AppCompatTheme_actionModeSplitBackground = 28;
        public static int AppCompatTheme_actionModeStyle = 29;
        public static int AppCompatTheme_actionModeTheme = 30;
        public static int AppCompatTheme_actionModeWebSearchDrawable = 31;
        public static int AppCompatTheme_actionOverflowButtonStyle = 32;
        public static int AppCompatTheme_actionOverflowMenuStyle = 33;
        public static int AppCompatTheme_activityChooserViewStyle = 34;
        public static int AppCompatTheme_alertDialogButtonGroupStyle = 35;
        public static int AppCompatTheme_alertDialogCenterButtons = 36;
        public static int AppCompatTheme_alertDialogStyle = 37;
        public static int AppCompatTheme_alertDialogTheme = 38;
        public static int AppCompatTheme_android_windowAnimationStyle = 1;
        public static int AppCompatTheme_android_windowIsFloating = 0;
        public static int AppCompatTheme_autoCompleteTextViewStyle = 39;
        public static int AppCompatTheme_borderlessButtonStyle = 40;
        public static int AppCompatTheme_buttonBarButtonStyle = 41;
        public static int AppCompatTheme_buttonBarNegativeButtonStyle = 42;
        public static int AppCompatTheme_buttonBarNeutralButtonStyle = 43;
        public static int AppCompatTheme_buttonBarPositiveButtonStyle = 44;
        public static int AppCompatTheme_buttonBarStyle = 45;
        public static int AppCompatTheme_buttonStyle = 46;
        public static int AppCompatTheme_buttonStyleSmall = 47;
        public static int AppCompatTheme_checkboxStyle = 48;
        public static int AppCompatTheme_checkedTextViewStyle = 49;
        public static int AppCompatTheme_colorAccent = 50;
        public static int AppCompatTheme_colorBackgroundFloating = 51;
        public static int AppCompatTheme_colorButtonNormal = 52;
        public static int AppCompatTheme_colorControlActivated = 53;
        public static int AppCompatTheme_colorControlHighlight = 54;
        public static int AppCompatTheme_colorControlNormal = 55;
        public static int AppCompatTheme_colorError = 56;
        public static int AppCompatTheme_colorPrimary = 57;
        public static int AppCompatTheme_colorPrimaryDark = 58;
        public static int AppCompatTheme_colorSwitchThumbNormal = 59;
        public static int AppCompatTheme_controlBackground = 60;
        public static int AppCompatTheme_dialogCornerRadius = 61;
        public static int AppCompatTheme_dialogPreferredPadding = 62;
        public static int AppCompatTheme_dialogTheme = 63;
        public static int AppCompatTheme_dividerHorizontal = 64;
        public static int AppCompatTheme_dividerVertical = 65;
        public static int AppCompatTheme_dropDownListViewStyle = 66;
        public static int AppCompatTheme_dropdownListPreferredItemHeight = 67;
        public static int AppCompatTheme_editTextBackground = 68;
        public static int AppCompatTheme_editTextColor = 69;
        public static int AppCompatTheme_editTextStyle = 70;
        public static int AppCompatTheme_homeAsUpIndicator = 71;
        public static int AppCompatTheme_imageButtonStyle = 72;
        public static int AppCompatTheme_listChoiceBackgroundIndicator = 73;
        public static int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 74;
        public static int AppCompatTheme_listChoiceIndicatorSingleAnimated = 75;
        public static int AppCompatTheme_listDividerAlertDialog = 76;
        public static int AppCompatTheme_listMenuViewStyle = 77;
        public static int AppCompatTheme_listPopupWindowStyle = 78;
        public static int AppCompatTheme_listPreferredItemHeight = 79;
        public static int AppCompatTheme_listPreferredItemHeightLarge = 80;
        public static int AppCompatTheme_listPreferredItemHeightSmall = 81;
        public static int AppCompatTheme_listPreferredItemPaddingEnd = 82;
        public static int AppCompatTheme_listPreferredItemPaddingLeft = 83;
        public static int AppCompatTheme_listPreferredItemPaddingRight = 84;
        public static int AppCompatTheme_listPreferredItemPaddingStart = 85;
        public static int AppCompatTheme_panelBackground = 86;
        public static int AppCompatTheme_panelMenuListTheme = 87;
        public static int AppCompatTheme_panelMenuListWidth = 88;
        public static int AppCompatTheme_popupMenuStyle = 89;
        public static int AppCompatTheme_popupWindowStyle = 90;
        public static int AppCompatTheme_radioButtonStyle = 91;
        public static int AppCompatTheme_ratingBarStyle = 92;
        public static int AppCompatTheme_ratingBarStyleIndicator = 93;
        public static int AppCompatTheme_ratingBarStyleSmall = 94;
        public static int AppCompatTheme_searchViewStyle = 95;
        public static int AppCompatTheme_seekBarStyle = 96;
        public static int AppCompatTheme_selectableItemBackground = 97;
        public static int AppCompatTheme_selectableItemBackgroundBorderless = 98;
        public static int AppCompatTheme_spinnerDropDownItemStyle = 99;
        public static int AppCompatTheme_spinnerStyle = 100;
        public static int AppCompatTheme_switchStyle = 101;
        public static int AppCompatTheme_textAppearanceLargePopupMenu = 102;
        public static int AppCompatTheme_textAppearanceListItem = 103;
        public static int AppCompatTheme_textAppearanceListItemSecondary = 104;
        public static int AppCompatTheme_textAppearanceListItemSmall = 105;
        public static int AppCompatTheme_textAppearancePopupMenuHeader = 106;
        public static int AppCompatTheme_textAppearanceSearchResultSubtitle = 107;
        public static int AppCompatTheme_textAppearanceSearchResultTitle = 108;
        public static int AppCompatTheme_textAppearanceSmallPopupMenu = 109;
        public static int AppCompatTheme_textColorAlertDialogListItem = 110;
        public static int AppCompatTheme_textColorSearchUrl = 111;
        public static int AppCompatTheme_toolbarNavigationButtonStyle = 112;
        public static int AppCompatTheme_toolbarStyle = 113;
        public static int AppCompatTheme_tooltipForegroundColor = 114;
        public static int AppCompatTheme_tooltipFrameBackground = 115;
        public static int AppCompatTheme_viewInflaterClass = 116;
        public static int AppCompatTheme_windowActionBar = 117;
        public static int AppCompatTheme_windowActionBarOverlay = 118;
        public static int AppCompatTheme_windowActionModeOverlay = 119;
        public static int AppCompatTheme_windowFixedHeightMajor = 120;
        public static int AppCompatTheme_windowFixedHeightMinor = 121;
        public static int AppCompatTheme_windowFixedWidthMajor = 122;
        public static int AppCompatTheme_windowFixedWidthMinor = 123;
        public static int AppCompatTheme_windowMinWidthMajor = 124;
        public static int AppCompatTheme_windowMinWidthMinor = 125;
        public static int AppCompatTheme_windowNoTitle = 126;
        public static int ButtonBarLayout_allowStacking = 0;
        public static int CheckedTextView_android_checkMark = 0;
        public static int CheckedTextView_checkMarkCompat = 1;
        public static int CheckedTextView_checkMarkTint = 2;
        public static int CheckedTextView_checkMarkTintMode = 3;
        public static int CompoundButton_android_button = 0;
        public static int CompoundButton_buttonCompat = 1;
        public static int CompoundButton_buttonTint = 2;
        public static int CompoundButton_buttonTintMode = 3;
        public static int DrawerArrowToggle_arrowHeadLength = 0;
        public static int DrawerArrowToggle_arrowShaftLength = 1;
        public static int DrawerArrowToggle_barLength = 2;
        public static int DrawerArrowToggle_color = 3;
        public static int DrawerArrowToggle_drawableSize = 4;
        public static int DrawerArrowToggle_gapBetweenBars = 5;
        public static int DrawerArrowToggle_spinBars = 6;
        public static int DrawerArrowToggle_thickness = 7;
        public static int LinearLayoutCompat_Layout_android_layout_gravity = 0;
        public static int LinearLayoutCompat_Layout_android_layout_height = 2;
        public static int LinearLayoutCompat_Layout_android_layout_weight = 3;
        public static int LinearLayoutCompat_Layout_android_layout_width = 1;
        public static int LinearLayoutCompat_android_baselineAligned = 2;
        public static int LinearLayoutCompat_android_baselineAlignedChildIndex = 3;
        public static int LinearLayoutCompat_android_gravity = 0;
        public static int LinearLayoutCompat_android_orientation = 1;
        public static int LinearLayoutCompat_android_weightSum = 4;
        public static int LinearLayoutCompat_divider = 5;
        public static int LinearLayoutCompat_dividerPadding = 6;
        public static int LinearLayoutCompat_measureWithLargestChild = 7;
        public static int LinearLayoutCompat_showDividers = 8;
        public static int ListPopupWindow_android_dropDownHorizontalOffset = 0;
        public static int ListPopupWindow_android_dropDownVerticalOffset = 1;
        public static int MenuGroup_android_checkableBehavior = 5;
        public static int MenuGroup_android_enabled = 0;
        public static int MenuGroup_android_id = 1;
        public static int MenuGroup_android_menuCategory = 3;
        public static int MenuGroup_android_orderInCategory = 4;
        public static int MenuGroup_android_visible = 2;
        public static int MenuItem_actionLayout = 13;
        public static int MenuItem_actionProviderClass = 14;
        public static int MenuItem_actionViewClass = 15;
        public static int MenuItem_alphabeticModifiers = 16;
        public static int MenuItem_android_alphabeticShortcut = 9;
        public static int MenuItem_android_checkable = 11;
        public static int MenuItem_android_checked = 3;
        public static int MenuItem_android_enabled = 1;
        public static int MenuItem_android_icon = 0;
        public static int MenuItem_android_id = 2;
        public static int MenuItem_android_menuCategory = 5;
        public static int MenuItem_android_numericShortcut = 10;
        public static int MenuItem_android_onClick = 12;
        public static int MenuItem_android_orderInCategory = 6;
        public static int MenuItem_android_title = 7;
        public static int MenuItem_android_titleCondensed = 8;
        public static int MenuItem_android_visible = 4;
        public static int MenuItem_contentDescription = 17;
        public static int MenuItem_iconTint = 18;
        public static int MenuItem_iconTintMode = 19;
        public static int MenuItem_numericModifiers = 20;
        public static int MenuItem_showAsAction = 21;
        public static int MenuItem_tooltipText = 22;
        public static int MenuView_android_headerBackground = 4;
        public static int MenuView_android_horizontalDivider = 2;
        public static int MenuView_android_itemBackground = 5;
        public static int MenuView_android_itemIconDisabledAlpha = 6;
        public static int MenuView_android_itemTextAppearance = 1;
        public static int MenuView_android_verticalDivider = 3;
        public static int MenuView_android_windowAnimationStyle = 0;
        public static int MenuView_preserveIconSpacing = 7;
        public static int MenuView_subMenuArrow = 8;
        public static int PopupWindowBackgroundState_state_above_anchor = 0;
        public static int PopupWindow_android_popupAnimationStyle = 1;
        public static int PopupWindow_android_popupBackground = 0;
        public static int PopupWindow_overlapAnchor = 2;
        public static int RecycleListView_paddingBottomNoButtons = 0;
        public static int RecycleListView_paddingTopNoTitle = 1;
        public static int SearchView_android_focusable = 1;
        public static int SearchView_android_hint = 4;
        public static int SearchView_android_imeOptions = 6;
        public static int SearchView_android_inputType = 5;
        public static int SearchView_android_maxWidth = 2;
        public static int SearchView_android_text = 3;
        public static int SearchView_android_textAppearance = 0;
        public static int SearchView_animateMenuItems = 7;
        public static int SearchView_animateNavigationIcon = 8;
        public static int SearchView_autoShowKeyboard = 9;
        public static int SearchView_backHandlingEnabled = 10;
        public static int SearchView_backgroundTint = 11;
        public static int SearchView_closeIcon = 12;
        public static int SearchView_commitIcon = 13;
        public static int SearchView_defaultQueryHint = 14;
        public static int SearchView_goIcon = 15;
        public static int SearchView_headerLayout = 16;
        public static int SearchView_hideNavigationIcon = 17;
        public static int SearchView_iconifiedByDefault = 18;
        public static int SearchView_layout = 19;
        public static int SearchView_queryBackground = 20;
        public static int SearchView_queryHint = 21;
        public static int SearchView_searchHintIcon = 22;
        public static int SearchView_searchIcon = 23;
        public static int SearchView_searchPrefixText = 24;
        public static int SearchView_submitBackground = 25;
        public static int SearchView_suggestionRowLayout = 26;
        public static int SearchView_useDrawerArrowDrawable = 27;
        public static int SearchView_voiceIcon = 28;
        public static int Spinner_android_dropDownWidth = 3;
        public static int Spinner_android_entries = 0;
        public static int Spinner_android_popupBackground = 1;
        public static int Spinner_android_prompt = 2;
        public static int Spinner_popupTheme = 4;
        public static int SwitchCompat_android_textOff = 1;
        public static int SwitchCompat_android_textOn = 0;
        public static int SwitchCompat_android_thumb = 2;
        public static int SwitchCompat_showText = 3;
        public static int SwitchCompat_splitTrack = 4;
        public static int SwitchCompat_switchMinWidth = 5;
        public static int SwitchCompat_switchPadding = 6;
        public static int SwitchCompat_switchTextAppearance = 7;
        public static int SwitchCompat_thumbTextPadding = 8;
        public static int SwitchCompat_thumbTint = 9;
        public static int SwitchCompat_thumbTintMode = 10;
        public static int SwitchCompat_track = 11;
        public static int SwitchCompat_trackTint = 12;
        public static int SwitchCompat_trackTintMode = 13;
        public static int TextAppearance_android_fontFamily = 10;
        public static int TextAppearance_android_shadowColor = 6;
        public static int TextAppearance_android_shadowDx = 7;
        public static int TextAppearance_android_shadowDy = 8;
        public static int TextAppearance_android_shadowRadius = 9;
        public static int TextAppearance_android_textColor = 3;
        public static int TextAppearance_android_textColorHint = 4;
        public static int TextAppearance_android_textColorLink = 5;
        public static int TextAppearance_android_textFontWeight = 11;
        public static int TextAppearance_android_textSize = 0;
        public static int TextAppearance_android_textStyle = 2;
        public static int TextAppearance_android_typeface = 1;
        public static int TextAppearance_fontFamily = 12;
        public static int TextAppearance_fontVariationSettings = 13;
        public static int TextAppearance_textAllCaps = 14;
        public static int TextAppearance_textLocale = 15;
        public static int Toolbar_android_gravity = 0;
        public static int Toolbar_android_minHeight = 1;
        public static int Toolbar_buttonGravity = 2;
        public static int Toolbar_collapseContentDescription = 3;
        public static int Toolbar_collapseIcon = 4;
        public static int Toolbar_contentInsetEnd = 5;
        public static int Toolbar_contentInsetEndWithActions = 6;
        public static int Toolbar_contentInsetLeft = 7;
        public static int Toolbar_contentInsetRight = 8;
        public static int Toolbar_contentInsetStart = 9;
        public static int Toolbar_contentInsetStartWithNavigation = 10;
        public static int Toolbar_logo = 11;
        public static int Toolbar_logoDescription = 12;
        public static int Toolbar_maxButtonHeight = 13;
        public static int Toolbar_menu = 14;
        public static int Toolbar_navigationContentDescription = 15;
        public static int Toolbar_navigationIcon = 16;
        public static int Toolbar_popupTheme = 17;
        public static int Toolbar_subtitle = 18;
        public static int Toolbar_subtitleTextAppearance = 19;
        public static int Toolbar_subtitleTextColor = 20;
        public static int Toolbar_title = 21;
        public static int Toolbar_titleMargin = 22;
        public static int Toolbar_titleMarginBottom = 23;
        public static int Toolbar_titleMarginEnd = 24;
        public static int Toolbar_titleMarginStart = 25;
        public static int Toolbar_titleMarginTop = 26;
        public static int Toolbar_titleMargins = 27;
        public static int Toolbar_titleTextAppearance = 28;
        public static int Toolbar_titleTextColor = 29;
        public static int ViewBackgroundHelper_android_background = 0;
        public static int ViewBackgroundHelper_backgroundTint = 1;
        public static int ViewBackgroundHelper_backgroundTintMode = 2;
        public static int ViewStubCompat_android_id = 0;
        public static int ViewStubCompat_android_inflatedId = 2;
        public static int ViewStubCompat_android_layout = 1;
        public static int View_android_focusable = 1;
        public static int View_android_theme = 0;
        public static int View_paddingEnd = 2;
        public static int View_paddingStart = 3;
        public static int View_theme = 4;
        public static int[] ActionBar = {C1201R.attr.background, C1201R.attr.backgroundSplit, C1201R.attr.backgroundStacked, C1201R.attr.contentInsetEnd, C1201R.attr.contentInsetEndWithActions, C1201R.attr.contentInsetLeft, C1201R.attr.contentInsetRight, C1201R.attr.contentInsetStart, C1201R.attr.contentInsetStartWithNavigation, C1201R.attr.customNavigationLayout, C1201R.attr.displayOptions, C1201R.attr.divider, C1201R.attr.elevation, C1201R.attr.height, C1201R.attr.hideOnContentScroll, C1201R.attr.homeAsUpIndicator, C1201R.attr.homeLayout, C1201R.attr.icon, C1201R.attr.indeterminateProgressStyle, C1201R.attr.itemPadding, C1201R.attr.logo, C1201R.attr.navigationMode, C1201R.attr.popupTheme, C1201R.attr.progressBarPadding, C1201R.attr.progressBarStyle, C1201R.attr.subtitle, C1201R.attr.subtitleTextStyle, C1201R.attr.title, C1201R.attr.titleTextStyle};
        public static int[] ActionBarLayout = {android.R.attr.layout_gravity};
        public static int[] ActionMenuItemView = {android.R.attr.minWidth};
        public static int[] ActionMenuView = new int[0];
        public static int[] ActionMode = {C1201R.attr.background, C1201R.attr.backgroundSplit, C1201R.attr.closeItemLayout, C1201R.attr.height, C1201R.attr.subtitleTextStyle, C1201R.attr.titleTextStyle};
        public static int[] ActivityChooserView = {C1201R.attr.expandActivityOverflowButtonDrawable, C1201R.attr.initialActivityCount};
        public static int[] AlertDialog = {android.R.attr.layout, C1201R.attr.buttonIconDimen, C1201R.attr.buttonPanelSideLayout, C1201R.attr.listItemLayout, C1201R.attr.listLayout, C1201R.attr.multiChoiceItemLayout, C1201R.attr.showTitle, C1201R.attr.singleChoiceItemLayout};
        public static int[] AppCompatEmojiHelper = new int[0];
        public static int[] AppCompatImageView = {android.R.attr.src, C1201R.attr.srcCompat, C1201R.attr.tint, C1201R.attr.tintMode};
        public static int[] AppCompatSeekBar = {android.R.attr.thumb, C1201R.attr.tickMark, C1201R.attr.tickMarkTint, C1201R.attr.tickMarkTintMode};
        public static int[] AppCompatTextHelper = {android.R.attr.textAppearance, android.R.attr.drawableTop, android.R.attr.drawableBottom, android.R.attr.drawableLeft, android.R.attr.drawableRight, android.R.attr.drawableStart, android.R.attr.drawableEnd};
        public static int[] AppCompatTextView = {android.R.attr.textAppearance, C1201R.attr.autoSizeMaxTextSize, C1201R.attr.autoSizeMinTextSize, C1201R.attr.autoSizePresetSizes, C1201R.attr.autoSizeStepGranularity, C1201R.attr.autoSizeTextType, C1201R.attr.drawableBottomCompat, C1201R.attr.drawableEndCompat, C1201R.attr.drawableLeftCompat, C1201R.attr.drawableRightCompat, C1201R.attr.drawableStartCompat, C1201R.attr.drawableTint, C1201R.attr.drawableTintMode, C1201R.attr.drawableTopCompat, C1201R.attr.emojiCompatEnabled, C1201R.attr.firstBaselineToTopHeight, C1201R.attr.fontFamily, C1201R.attr.fontVariationSettings, C1201R.attr.lastBaselineToBottomHeight, C1201R.attr.lineHeight, C1201R.attr.textAllCaps, C1201R.attr.textLocale};
        public static int[] AppCompatTheme = {android.R.attr.windowIsFloating, android.R.attr.windowAnimationStyle, C1201R.attr.actionBarDivider, C1201R.attr.actionBarItemBackground, C1201R.attr.actionBarPopupTheme, C1201R.attr.actionBarSize, C1201R.attr.actionBarSplitStyle, C1201R.attr.actionBarStyle, C1201R.attr.actionBarTabBarStyle, C1201R.attr.actionBarTabStyle, C1201R.attr.actionBarTabTextStyle, C1201R.attr.actionBarTheme, C1201R.attr.actionBarWidgetTheme, C1201R.attr.actionButtonStyle, C1201R.attr.actionDropDownStyle, C1201R.attr.actionMenuTextAppearance, C1201R.attr.actionMenuTextColor, C1201R.attr.actionModeBackground, C1201R.attr.actionModeCloseButtonStyle, C1201R.attr.actionModeCloseContentDescription, C1201R.attr.actionModeCloseDrawable, C1201R.attr.actionModeCopyDrawable, C1201R.attr.actionModeCutDrawable, C1201R.attr.actionModeFindDrawable, C1201R.attr.actionModePasteDrawable, C1201R.attr.actionModePopupWindowStyle, C1201R.attr.actionModeSelectAllDrawable, C1201R.attr.actionModeShareDrawable, C1201R.attr.actionModeSplitBackground, C1201R.attr.actionModeStyle, C1201R.attr.actionModeTheme, C1201R.attr.actionModeWebSearchDrawable, C1201R.attr.actionOverflowButtonStyle, C1201R.attr.actionOverflowMenuStyle, C1201R.attr.activityChooserViewStyle, C1201R.attr.alertDialogButtonGroupStyle, C1201R.attr.alertDialogCenterButtons, C1201R.attr.alertDialogStyle, C1201R.attr.alertDialogTheme, C1201R.attr.autoCompleteTextViewStyle, C1201R.attr.borderlessButtonStyle, C1201R.attr.buttonBarButtonStyle, C1201R.attr.buttonBarNegativeButtonStyle, C1201R.attr.buttonBarNeutralButtonStyle, C1201R.attr.buttonBarPositiveButtonStyle, C1201R.attr.buttonBarStyle, C1201R.attr.buttonStyle, C1201R.attr.buttonStyleSmall, C1201R.attr.checkboxStyle, C1201R.attr.checkedTextViewStyle, C1201R.attr.colorAccent, C1201R.attr.colorBackgroundFloating, C1201R.attr.colorButtonNormal, C1201R.attr.colorControlActivated, C1201R.attr.colorControlHighlight, C1201R.attr.colorControlNormal, C1201R.attr.colorError, C1201R.attr.colorPrimary, C1201R.attr.colorPrimaryDark, C1201R.attr.colorSwitchThumbNormal, C1201R.attr.controlBackground, C1201R.attr.dialogCornerRadius, C1201R.attr.dialogPreferredPadding, C1201R.attr.dialogTheme, C1201R.attr.dividerHorizontal, C1201R.attr.dividerVertical, C1201R.attr.dropDownListViewStyle, C1201R.attr.dropdownListPreferredItemHeight, C1201R.attr.editTextBackground, C1201R.attr.editTextColor, C1201R.attr.editTextStyle, C1201R.attr.homeAsUpIndicator, C1201R.attr.imageButtonStyle, C1201R.attr.listChoiceBackgroundIndicator, C1201R.attr.listChoiceIndicatorMultipleAnimated, C1201R.attr.listChoiceIndicatorSingleAnimated, C1201R.attr.listDividerAlertDialog, C1201R.attr.listMenuViewStyle, C1201R.attr.listPopupWindowStyle, C1201R.attr.listPreferredItemHeight, C1201R.attr.listPreferredItemHeightLarge, C1201R.attr.listPreferredItemHeightSmall, C1201R.attr.listPreferredItemPaddingEnd, C1201R.attr.listPreferredItemPaddingLeft, C1201R.attr.listPreferredItemPaddingRight, C1201R.attr.listPreferredItemPaddingStart, C1201R.attr.panelBackground, C1201R.attr.panelMenuListTheme, C1201R.attr.panelMenuListWidth, C1201R.attr.popupMenuStyle, C1201R.attr.popupWindowStyle, C1201R.attr.radioButtonStyle, C1201R.attr.ratingBarStyle, C1201R.attr.ratingBarStyleIndicator, C1201R.attr.ratingBarStyleSmall, C1201R.attr.searchViewStyle, C1201R.attr.seekBarStyle, C1201R.attr.selectableItemBackground, C1201R.attr.selectableItemBackgroundBorderless, C1201R.attr.spinnerDropDownItemStyle, C1201R.attr.spinnerStyle, C1201R.attr.switchStyle, C1201R.attr.textAppearanceLargePopupMenu, C1201R.attr.textAppearanceListItem, C1201R.attr.textAppearanceListItemSecondary, C1201R.attr.textAppearanceListItemSmall, C1201R.attr.textAppearancePopupMenuHeader, C1201R.attr.textAppearanceSearchResultSubtitle, C1201R.attr.textAppearanceSearchResultTitle, C1201R.attr.textAppearanceSmallPopupMenu, C1201R.attr.textColorAlertDialogListItem, C1201R.attr.textColorSearchUrl, C1201R.attr.toolbarNavigationButtonStyle, C1201R.attr.toolbarStyle, C1201R.attr.tooltipForegroundColor, C1201R.attr.tooltipFrameBackground, C1201R.attr.viewInflaterClass, C1201R.attr.windowActionBar, C1201R.attr.windowActionBarOverlay, C1201R.attr.windowActionModeOverlay, C1201R.attr.windowFixedHeightMajor, C1201R.attr.windowFixedHeightMinor, C1201R.attr.windowFixedWidthMajor, C1201R.attr.windowFixedWidthMinor, C1201R.attr.windowMinWidthMajor, C1201R.attr.windowMinWidthMinor, C1201R.attr.windowNoTitle};
        public static int[] ButtonBarLayout = {C1201R.attr.allowStacking};
        public static int[] CheckedTextView = {android.R.attr.checkMark, C1201R.attr.checkMarkCompat, C1201R.attr.checkMarkTint, C1201R.attr.checkMarkTintMode};
        public static int[] CompoundButton = {android.R.attr.button, C1201R.attr.buttonCompat, C1201R.attr.buttonTint, C1201R.attr.buttonTintMode};
        public static int[] DrawerArrowToggle = {C1201R.attr.arrowHeadLength, C1201R.attr.arrowShaftLength, C1201R.attr.barLength, C1201R.attr.color, C1201R.attr.drawableSize, C1201R.attr.gapBetweenBars, C1201R.attr.spinBars, C1201R.attr.thickness};
        public static int[] LinearLayoutCompat = {android.R.attr.gravity, android.R.attr.orientation, android.R.attr.baselineAligned, android.R.attr.baselineAlignedChildIndex, android.R.attr.weightSum, C1201R.attr.divider, C1201R.attr.dividerPadding, C1201R.attr.measureWithLargestChild, C1201R.attr.showDividers};
        public static int[] LinearLayoutCompat_Layout = {android.R.attr.layout_gravity, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_weight};
        public static int[] ListPopupWindow = {android.R.attr.dropDownHorizontalOffset, android.R.attr.dropDownVerticalOffset};
        public static int[] MenuGroup = {android.R.attr.enabled, android.R.attr.id, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.checkableBehavior};
        public static int[] MenuItem = {android.R.attr.icon, android.R.attr.enabled, android.R.attr.id, android.R.attr.checked, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.title, android.R.attr.titleCondensed, android.R.attr.alphabeticShortcut, android.R.attr.numericShortcut, android.R.attr.checkable, android.R.attr.onClick, C1201R.attr.actionLayout, C1201R.attr.actionProviderClass, C1201R.attr.actionViewClass, C1201R.attr.alphabeticModifiers, C1201R.attr.contentDescription, C1201R.attr.iconTint, C1201R.attr.iconTintMode, C1201R.attr.numericModifiers, C1201R.attr.showAsAction, C1201R.attr.tooltipText};
        public static int[] MenuView = {android.R.attr.windowAnimationStyle, android.R.attr.itemTextAppearance, android.R.attr.horizontalDivider, android.R.attr.verticalDivider, android.R.attr.headerBackground, android.R.attr.itemBackground, android.R.attr.itemIconDisabledAlpha, C1201R.attr.preserveIconSpacing, C1201R.attr.subMenuArrow};
        public static int[] PopupWindow = {android.R.attr.popupBackground, android.R.attr.popupAnimationStyle, C1201R.attr.overlapAnchor};
        public static int[] PopupWindowBackgroundState = {C1201R.attr.state_above_anchor};
        public static int[] RecycleListView = {C1201R.attr.paddingBottomNoButtons, C1201R.attr.paddingTopNoTitle};
        public static int[] SearchView = {android.R.attr.textAppearance, android.R.attr.focusable, android.R.attr.maxWidth, android.R.attr.text, android.R.attr.hint, android.R.attr.inputType, android.R.attr.imeOptions, C1201R.attr.animateMenuItems, C1201R.attr.animateNavigationIcon, C1201R.attr.autoShowKeyboard, C1201R.attr.backHandlingEnabled, C1201R.attr.backgroundTint, C1201R.attr.closeIcon, C1201R.attr.commitIcon, C1201R.attr.defaultQueryHint, C1201R.attr.goIcon, C1201R.attr.headerLayout, C1201R.attr.hideNavigationIcon, C1201R.attr.iconifiedByDefault, C1201R.attr.layout, C1201R.attr.queryBackground, C1201R.attr.queryHint, C1201R.attr.searchHintIcon, C1201R.attr.searchIcon, C1201R.attr.searchPrefixText, C1201R.attr.submitBackground, C1201R.attr.suggestionRowLayout, C1201R.attr.useDrawerArrowDrawable, C1201R.attr.voiceIcon};
        public static int[] Spinner = {android.R.attr.entries, android.R.attr.popupBackground, android.R.attr.prompt, android.R.attr.dropDownWidth, C1201R.attr.popupTheme};
        public static int[] SwitchCompat = {android.R.attr.textOn, android.R.attr.textOff, android.R.attr.thumb, C1201R.attr.showText, C1201R.attr.splitTrack, C1201R.attr.switchMinWidth, C1201R.attr.switchPadding, C1201R.attr.switchTextAppearance, C1201R.attr.thumbTextPadding, C1201R.attr.thumbTint, C1201R.attr.thumbTintMode, C1201R.attr.track, C1201R.attr.trackTint, C1201R.attr.trackTintMode};
        public static int[] TextAppearance = {android.R.attr.textSize, android.R.attr.typeface, android.R.attr.textStyle, android.R.attr.textColor, android.R.attr.textColorHint, android.R.attr.textColorLink, android.R.attr.shadowColor, android.R.attr.shadowDx, android.R.attr.shadowDy, android.R.attr.shadowRadius, android.R.attr.fontFamily, android.R.attr.textFontWeight, C1201R.attr.fontFamily, C1201R.attr.fontVariationSettings, C1201R.attr.textAllCaps, C1201R.attr.textLocale};
        public static int[] Toolbar = {android.R.attr.gravity, android.R.attr.minHeight, C1201R.attr.buttonGravity, C1201R.attr.collapseContentDescription, C1201R.attr.collapseIcon, C1201R.attr.contentInsetEnd, C1201R.attr.contentInsetEndWithActions, C1201R.attr.contentInsetLeft, C1201R.attr.contentInsetRight, C1201R.attr.contentInsetStart, C1201R.attr.contentInsetStartWithNavigation, C1201R.attr.logo, C1201R.attr.logoDescription, C1201R.attr.maxButtonHeight, C1201R.attr.menu, C1201R.attr.navigationContentDescription, C1201R.attr.navigationIcon, C1201R.attr.popupTheme, C1201R.attr.subtitle, C1201R.attr.subtitleTextAppearance, C1201R.attr.subtitleTextColor, C1201R.attr.title, C1201R.attr.titleMargin, C1201R.attr.titleMarginBottom, C1201R.attr.titleMarginEnd, C1201R.attr.titleMarginStart, C1201R.attr.titleMarginTop, C1201R.attr.titleMargins, C1201R.attr.titleTextAppearance, C1201R.attr.titleTextColor};
        public static int[] View = {android.R.attr.theme, android.R.attr.focusable, C1201R.attr.paddingEnd, C1201R.attr.paddingStart, C1201R.attr.theme};
        public static int[] ViewBackgroundHelper = {android.R.attr.background, C1201R.attr.backgroundTint, C1201R.attr.backgroundTintMode};
        public static int[] ViewStubCompat = {android.R.attr.id, android.R.attr.layout, android.R.attr.inflatedId};

        private styleable() {
        }
    }

    private C0037R() {
    }
}
