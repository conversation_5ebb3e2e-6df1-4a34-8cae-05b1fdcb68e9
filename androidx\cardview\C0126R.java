package androidx.cardview;

import com.nsdisplay.a01.C1201R;

/* renamed from: androidx.cardview.R */
/* loaded from: classes3.dex */
public final class C0126R {

    /* renamed from: androidx.cardview.R$attr */
    public static final class attr {
        public static int cardBackgroundColor = 2130903199;
        public static int cardCornerRadius = 2130903200;
        public static int cardElevation = 2130903201;
        public static int cardMaxElevation = 2130903203;
        public static int cardPreventCornerOverlap = 2130903204;
        public static int cardUseCompatPadding = 2130903205;
        public static int cardViewStyle = 2130903206;
        public static int contentPadding = 2130903366;
        public static int contentPaddingBottom = 2130903367;
        public static int contentPaddingLeft = 2130903369;
        public static int contentPaddingRight = 2130903370;
        public static int contentPaddingTop = 2130903372;

        private attr() {
        }
    }

    /* renamed from: androidx.cardview.R$color */
    public static final class color {
        public static int cardview_dark_background = 2131034160;
        public static int cardview_light_background = 2131034161;
        public static int cardview_shadow_end_color = 2131034162;
        public static int cardview_shadow_start_color = 2131034163;

        private color() {
        }
    }

    /* renamed from: androidx.cardview.R$dimen */
    public static final class dimen {
        public static int cardview_compat_inset_shadow = 2131099732;
        public static int cardview_default_elevation = 2131099733;
        public static int cardview_default_radius = 2131099734;

        private dimen() {
        }
    }

    /* renamed from: androidx.cardview.R$style */
    public static final class style {
        public static int Base_CardView = 2131886096;
        public static int CardView = 2131886375;
        public static int CardView_Dark = 2131886376;
        public static int CardView_Light = 2131886377;

        private style() {
        }
    }

    /* renamed from: androidx.cardview.R$styleable */
    public static final class styleable {
        public static int[] CardView = {android.R.attr.minWidth, android.R.attr.minHeight, C1201R.attr.cardBackgroundColor, C1201R.attr.cardCornerRadius, C1201R.attr.cardElevation, C1201R.attr.cardMaxElevation, C1201R.attr.cardPreventCornerOverlap, C1201R.attr.cardUseCompatPadding, C1201R.attr.contentPadding, C1201R.attr.contentPaddingBottom, C1201R.attr.contentPaddingLeft, C1201R.attr.contentPaddingRight, C1201R.attr.contentPaddingTop};
        public static int CardView_android_minHeight = 1;
        public static int CardView_android_minWidth = 0;
        public static int CardView_cardBackgroundColor = 2;
        public static int CardView_cardCornerRadius = 3;
        public static int CardView_cardElevation = 4;
        public static int CardView_cardMaxElevation = 5;
        public static int CardView_cardPreventCornerOverlap = 6;
        public static int CardView_cardUseCompatPadding = 7;
        public static int CardView_contentPadding = 8;
        public static int CardView_contentPaddingBottom = 9;
        public static int CardView_contentPaddingLeft = 10;
        public static int CardView_contentPaddingRight = 11;
        public static int CardView_contentPaddingTop = 12;

        private styleable() {
        }
    }

    private C0126R() {
    }
}
