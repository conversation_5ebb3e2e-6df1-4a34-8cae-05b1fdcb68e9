package androidx.constraintlayout.core.widgets;

/* loaded from: classes.dex */
public class Rectangle {
    public int height;
    public int width;

    /* renamed from: x */
    public int f46x;

    /* renamed from: y */
    public int f47y;

    public void setBounds(int x, int y, int width, int height) {
        this.f46x = x;
        this.f47y = y;
        this.width = width;
        this.height = height;
    }

    void grow(int w, int h) {
        this.f46x -= w;
        this.f47y -= h;
        this.width += w * 2;
        this.height += h * 2;
    }

    boolean intersects(Rectangle bounds) {
        int i;
        int i2;
        int i3 = this.f46x;
        int i4 = bounds.f46x;
        return i3 >= i4 && i3 < i4 + bounds.width && (i = this.f47y) >= (i2 = bounds.f47y) && i < i2 + bounds.height;
    }

    public boolean contains(int x, int y) {
        int i;
        int i2 = this.f46x;
        return x >= i2 && x < i2 + this.width && y >= (i = this.f47y) && y < i + this.height;
    }

    public int getCenterX() {
        return (this.f46x + this.width) / 2;
    }

    public int getCenterY() {
        return (this.f47y + this.height) / 2;
    }
}
